import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function getMaturityColor(maturity: string): string {
  switch (maturity) {
    case 'graduated':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'incubating':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'sandbox':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'archived':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export function getMaturityLabel(maturity: string): string {
  switch (maturity) {
    case 'graduated':
      return '已毕业'
    case 'incubating':
      return '孵化中'
    case 'sandbox':
      return '沙盒'
    case 'archived':
      return '已归档'
    default:
      return maturity
  }
}
