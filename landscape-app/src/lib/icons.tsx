import {
  Cloud,
  Database,
  Network,
  Shield,
  Monitor,
  Container,
  Server,
  Cpu,
  HardDrive,
  Layers,
  Settings,
  Globe,
  Lock,
  Activity,
  BarChart3,
  Zap,
  Workflow,
  GitBranch,
  Box,
  Gauge,
  Eye,
  FileText,
  Users,
  Cog
} from 'lucide-react'

// 技术类别图标映射
export const categoryIcons = {
  'container-runtime': Container,
  'orchestration': Workflow,
  'observability': Eye,
  'security': Shield,
  'storage': Database,
  'networking': Network,
  'compute': Cpu,
  'platform': Cloud,
  'monitoring': Monitor,
  'management': Settings
}

// 项目特定图标映射
export const projectIcons = {
  'docker': Container,
  'kubernetes': Workflow,
  'prometheus': Activity,
  'grafana': BarChart3,
  'containerd': Box,
  'helm': GitBranch,
  'istio': Network,
  'etcd': Database,
  'falco': Shield,
  'jaeger': Gauge,
  'zstack-zaku': Cloud,
  'zstack-rds': Database,
  'zstack-sds': HardDrive,
  'multi-tenant': Users,
  'sdn': Network,
  'cmp': Settings,
  'compute-mgmt': Cpu,
  'monitoring': Monitor,
  'vmware-mgmt': Server,
  'zsphere-mgmt': Cog,
  'kvm-virt': Server,
  'network-virt': Network,
  'storage-virt': HardDrive
}

// 硬件类型图标
export const hardwareIcons = {
  'intel': Cpu,
  'amd': Cpu,
  'arm': Cpu,
  'nvidia': Zap,
  'storage': HardDrive,
  'server': Server
}

// 获取项目图标
export function getProjectIcon(projectId: string) {
  return projectIcons[projectId as keyof typeof projectIcons] || Box
}

// 获取分类图标
export function getCategoryIcon(categoryId: string) {
  return categoryIcons[categoryId as keyof typeof categoryIcons] || Layers
}

// 获取硬件图标
export function getHardwareIcon(hardwareType: string) {
  return hardwareIcons[hardwareType as keyof typeof hardwareIcons] || Server
}
