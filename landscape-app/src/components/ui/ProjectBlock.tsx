'use client'

import { Project } from '@/types/landscape'
import { ExternalLink, Github } from 'lucide-react'

interface ProjectBlockProps {
  project: Project
  onClick?: () => void
  isHighlighted?: boolean
  size?: 'small' | 'medium' | 'large'
}

export function ProjectBlock({ project, onClick, isHighlighted, size = 'medium' }: ProjectBlockProps) {
  const sizeClasses = {
    small: 'px-2 py-1 text-xs min-w-[60px] h-8',
    medium: 'px-3 py-2 text-sm min-w-[80px] h-10',
    large: 'px-4 py-3 text-base min-w-[120px] h-12'
  }

  const getMaturityColor = (maturity: string) => {
    switch (maturity) {
      case 'graduated':
        return 'bg-green-500 border-green-600 text-white'
      case 'incubating':
        return 'bg-blue-500 border-blue-600 text-white'
      case 'sandbox':
        return 'bg-yellow-500 border-yellow-600 text-white'
      case 'archived':
        return 'bg-gray-500 border-gray-600 text-white'
      default:
        return 'bg-gray-400 border-gray-500 text-white'
    }
  }

  const baseClasses = `
    relative rounded border-2 cursor-pointer transition-all duration-200
    flex items-center justify-center text-center font-medium
    hover:shadow-lg hover:scale-105 group
    ${sizeClasses[size]}
    ${isHighlighted ? 'ring-2 ring-yellow-400 ring-offset-2' : ''}
    ${project.color ? '' : getMaturityColor(project.maturity)}
  `

  const customStyle = project.color ? {
    backgroundColor: project.color,
    borderColor: project.color,
    color: 'white'
  } : {}

  return (
    <div
      className={baseClasses}
      style={customStyle}
      onClick={onClick}
      title={`${project.name} - ${project.description}`}
    >
      {/* 项目名称 */}
      <span className="truncate">{project.name}</span>

      {/* 悬停时显示的操作按钮 */}
      <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
        {project.homepage_url && (
          <a
            href={project.homepage_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink className="w-3 h-3 text-gray-600" />
          </a>
        )}
        {project.github_url && (
          <a
            href={project.github_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100"
            onClick={(e) => e.stopPropagation()}
          >
            <Github className="w-3 h-3 text-gray-600" />
          </a>
        )}
      </div>

      {/* 成熟度指示器 */}
      <div className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white"
           style={{
             backgroundColor: project.maturity === 'graduated' ? '#10b981' :
                            project.maturity === 'incubating' ? '#3b82f6' :
                            project.maturity === 'sandbox' ? '#f59e0b' : '#6b7280'
           }}
           title={`成熟度: ${project.maturity}`}
      />
    </div>
  )
}
