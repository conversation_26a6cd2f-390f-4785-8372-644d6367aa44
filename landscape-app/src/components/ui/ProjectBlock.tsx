'use client'

import { Project } from '@/types/landscape'
import { ExternalLink, Github } from 'lucide-react'
import { getProjectIcon } from '@/lib/icons'

interface ProjectBlockProps {
  project: Project
  onClick?: () => void
  isHighlighted?: boolean
  size?: 'small' | 'medium' | 'large'
}

export function ProjectBlock({ project, onClick, isHighlighted, size = 'medium' }: ProjectBlockProps) {
  const IconComponent = getProjectIcon(project.id)

  const sizeClasses = {
    small: 'px-3 py-2 text-xs min-w-[80px] h-10',
    medium: 'px-4 py-3 text-sm min-w-[100px] h-12',
    large: 'px-5 py-4 text-base min-w-[140px] h-16'
  }

  const iconSizes = {
    small: 'w-3 h-3',
    medium: 'w-4 h-4',
    large: 'w-5 h-5'
  }

  const getMaturityGradient = (maturity: string) => {
    switch (maturity) {
      case 'graduated':
        return 'bg-gradient-to-br from-emerald-500 to-green-600 border-emerald-400 text-white shadow-emerald-200'
      case 'incubating':
        return 'bg-gradient-to-br from-blue-500 to-indigo-600 border-blue-400 text-white shadow-blue-200'
      case 'sandbox':
        return 'bg-gradient-to-br from-amber-500 to-orange-600 border-amber-400 text-white shadow-amber-200'
      case 'archived':
        return 'bg-gradient-to-br from-gray-500 to-slate-600 border-gray-400 text-white shadow-gray-200'
      default:
        return 'bg-gradient-to-br from-gray-400 to-gray-500 border-gray-300 text-white shadow-gray-200'
    }
  }

  const baseClasses = `
    relative rounded-xl border-2 cursor-pointer transition-all duration-300 ease-out
    flex flex-col items-center justify-center text-center font-semibold
    hover:shadow-xl hover:scale-105 hover:-translate-y-1 group
    backdrop-blur-sm
    ${sizeClasses[size]}
    ${isHighlighted ? 'ring-4 ring-yellow-400 ring-offset-2 ring-offset-white' : ''}
    ${project.color ? 'text-white shadow-lg' : getMaturityGradient(project.maturity)}
  `

  const customStyle = project.color ? {
    background: `linear-gradient(135deg, ${project.color}, ${project.color}dd)`,
    borderColor: project.color,
    boxShadow: `0 10px 25px -5px ${project.color}40, 0 4px 6px -2px ${project.color}20`
  } : {}

  return (
    <div
      className={baseClasses}
      style={customStyle}
      onClick={onClick}
      title={`${project.name} - ${project.description}`}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 rounded-xl bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* 项目图标 */}
      <div className="flex items-center justify-center mb-1">
        <IconComponent className={`${iconSizes[size]} opacity-90`} />
      </div>

      {/* 项目名称 */}
      <span className="truncate text-center leading-tight">{project.name}</span>

      {/* 悬停时显示的操作按钮 */}
      <div className="absolute -top-3 -right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-100 scale-75 flex space-x-1">
        {project.homepage_url && (
          <a
            href={project.homepage_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-6 h-6 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink className="w-3 h-3 text-gray-700" />
          </a>
        )}
        {project.github_url && (
          <a
            href={project.github_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-6 h-6 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <Github className="w-3 h-3 text-gray-700" />
          </a>
        )}
      </div>

      {/* 成熟度指示器 - 更精美的设计 */}
      <div
        className="absolute -bottom-2 -right-2 w-4 h-4 rounded-full border-2 border-white shadow-lg"
        style={{
          backgroundColor: project.maturity === 'graduated' ? '#10b981' :
                         project.maturity === 'incubating' ? '#3b82f6' :
                         project.maturity === 'sandbox' ? '#f59e0b' : '#6b7280'
        }}
        title={`成熟度: ${project.maturity}`}
      >
        <div className="absolute inset-0.5 rounded-full bg-white/30" />
      </div>

      {/* 光晕效果 */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </div>
  )
}
