'use client'

import { ArchitectureLayer, Project } from '@/types/landscape'
import { ProjectBlock } from './ProjectBlock'
import { getCategoryIcon, getHardwareIcon } from '@/lib/icons'
import { Cpu, HardDrive, Server, Zap, ArrowDown, Layers } from 'lucide-react'

interface ArchitectureDiagramProps {
  layers: ArchitectureLayer[]
  onProjectClick?: (project: Project) => void
  searchTerm?: string
}

export function ArchitectureDiagram({ layers, onProjectClick, searchTerm }: ArchitectureDiagramProps) {
  const sortedLayers = [...layers].sort((a, b) => a.order - b.order)

  const highlightProject = (project: Project): boolean => {
    if (!searchTerm) return false
    const term = searchTerm.toLowerCase()
    return (
      project.name.toLowerCase().includes(term) ||
      project.description.toLowerCase().includes(term) ||
      project.tags.some(tag => tag.toLowerCase().includes(term))
    )
  }

  return (
    <div className="w-full bg-gradient-to-br from-slate-50 to-blue-50 p-8 rounded-2xl border border-slate-200 shadow-xl">
      {/* 架构图标题 */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-white/50">
          <Layers className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-800">云原生技术架构全景图</h2>
        </div>
      </div>

      <div className="space-y-8">
        {sortedLayers.map((layer, index) => (
          <div key={layer.id} className="relative">
            {/* 层间连接线 */}
            {index > 0 && (
              <div className="flex justify-center mb-4">
                <div className="flex flex-col items-center">
                  <ArrowDown className="w-6 h-6 text-gray-400 animate-pulse" />
                  <div className="w-px h-4 bg-gradient-to-b from-gray-300 to-transparent" />
                </div>
              </div>
            )}

            <div
              className="relative rounded-2xl border-2 p-6 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
              style={{
                background: `linear-gradient(135deg, ${layer.backgroundColor}, ${layer.backgroundColor}dd)`,
                borderColor: layer.borderColor,
                minHeight: `${layer.height}px`
              }}
            >
              {/* 层标题 - 重新设计 */}
              <div className="absolute -top-4 left-6 px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white/50"
                   style={{
                     background: `linear-gradient(135deg, ${layer.color}, ${layer.color}dd)`,
                     color: 'white'
                   }}>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-white/80 rounded-full animate-pulse" />
                  <span>{layer.name}</span>
                </div>
              </div>

            {/* 层内容区域 */}
            <div className="mt-4 flex flex-wrap gap-4 min-h-[120px]">
              {layer.sections.map((section) => (
                <div
                  key={section.id}
                  className="relative rounded border p-3 flex-1 min-w-[200px]"
                  style={{
                    backgroundColor: section.backgroundColor || 'rgba(255, 255, 255, 0.8)',
                    borderColor: section.borderColor || '#e5e7eb',
                    width: `${section.width * 100}%`
                  }}
                >
                  {/* 区域标题 */}
                  <div className="text-sm font-medium text-gray-700 mb-3 text-center">
                    {section.name}
                  </div>

                  {/* 项目块 */}
                  <div className="flex flex-wrap gap-2 justify-center">
                    {section.projects.map((project) => (
                      <ProjectBlock
                        key={project.id}
                        project={project}
                        onClick={() => onProjectClick?.(project)}
                        isHighlighted={highlightProject(project)}
                        size={project.size || 'medium'}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 底部硬件层 */}
      <div className="mt-4 grid grid-cols-3 gap-4">
        <div className="bg-blue-100 border-2 border-blue-300 rounded-lg p-4 text-center">
          <div className="text-sm font-medium text-blue-800 mb-2">服务器</div>
          <div className="flex justify-center space-x-2">
            <div className="w-8 h-6 bg-blue-600 rounded text-xs text-white flex items-center justify-center">Intel</div>
            <div className="w-8 h-6 bg-red-600 rounded text-xs text-white flex items-center justify-center">AMD</div>
            <div className="w-8 h-6 bg-green-600 rounded text-xs text-white flex items-center justify-center">ARM</div>
          </div>
        </div>
        
        <div className="bg-purple-100 border-2 border-purple-300 rounded-lg p-4 text-center">
          <div className="text-sm font-medium text-purple-800 mb-2">集中式存储设备</div>
          <div className="text-xs text-purple-600">网络存储 · SAN</div>
        </div>
        
        <div className="bg-yellow-100 border-2 border-yellow-300 rounded-lg p-4 text-center">
          <div className="text-sm font-medium text-yellow-800 mb-2">GPU</div>
          <div className="bg-green-600 text-white text-xs px-2 py-1 rounded">NVIDIA全系列</div>
        </div>
      </div>
    </div>
  )
}
