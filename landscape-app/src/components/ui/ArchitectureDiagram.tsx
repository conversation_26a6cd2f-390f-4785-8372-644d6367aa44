'use client'

import { ArchitectureLayer, Project } from '@/types/landscape'
import { ProjectBlock } from './ProjectBlock'
import { getCategoryIcon, getHardwareIcon } from '@/lib/icons'
import { Cpu, HardDrive, Server, Zap, ArrowDown, Layers } from 'lucide-react'

interface ArchitectureDiagramProps {
  layers: ArchitectureLayer[]
  onProjectClick?: (project: Project) => void
  searchTerm?: string
}

export function ArchitectureDiagram({ layers, onProjectClick, searchTerm }: ArchitectureDiagramProps) {
  const sortedLayers = [...layers].sort((a, b) => a.order - b.order)

  const highlightProject = (project: Project): boolean => {
    if (!searchTerm) return false
    const term = searchTerm.toLowerCase()
    return (
      project.name.toLowerCase().includes(term) ||
      project.description.toLowerCase().includes(term) ||
      project.tags.some(tag => tag.toLowerCase().includes(term))
    )
  }

  return (
    <div className="w-full bg-gradient-to-br from-slate-50 to-blue-50 p-8 rounded-2xl border border-slate-200 shadow-xl">
      {/* 架构图标题 */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-white/50">
          <Layers className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-800">云原生技术架构全景图</h2>
        </div>
      </div>

      <div className="space-y-8">
        {sortedLayers.map((layer, index) => (
          <div key={layer.id} className="relative">
            {/* 层间连接线 */}
            {index > 0 && (
              <div className="flex justify-center mb-4">
                <div className="flex flex-col items-center">
                  <ArrowDown className="w-6 h-6 text-gray-400 animate-pulse" />
                  <div className="w-px h-4 bg-gradient-to-b from-gray-300 to-transparent" />
                </div>
              </div>
            )}

            <div
              className="relative rounded-2xl border-2 p-6 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
              style={{
                background: `linear-gradient(135deg, ${layer.backgroundColor}, ${layer.backgroundColor}dd)`,
                borderColor: layer.borderColor,
                minHeight: `${layer.height}px`
              }}
            >
              {/* 层标题 - 重新设计 */}
              <div className="absolute -top-4 left-6 px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white/50"
                   style={{
                     background: `linear-gradient(135deg, ${layer.color}, ${layer.color}dd)`,
                     color: 'white'
                   }}>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-white/80 rounded-full animate-pulse" />
                  <span>{layer.name}</span>
                </div>
              </div>

            {/* 层内容区域 */}
            <div className="mt-8 flex flex-wrap gap-6 min-h-[140px]">
              {layer.sections.map((section) => (
                <div
                  key={section.id}
                  className="relative rounded-xl border-2 p-5 flex-1 min-w-[220px] backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300"
                  style={{
                    background: `linear-gradient(135deg, ${section.backgroundColor || 'rgba(255, 255, 255, 0.9)'}, ${section.backgroundColor || 'rgba(255, 255, 255, 0.7)'})`,
                    borderColor: section.borderColor || '#e5e7eb',
                    width: `${section.width * 100}%`
                  }}
                >
                  {/* 区域装饰线 */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent rounded-t-xl" />

                  {/* 区域标题 */}
                  <div className="text-sm font-bold text-gray-800 mb-4 text-center bg-white/50 backdrop-blur-sm rounded-lg py-2 px-3 border border-white/30">
                    {section.name}
                  </div>

                  {/* 项目块 */}
                  <div className="flex flex-wrap gap-3 justify-center">
                    {section.projects.map((project) => (
                      <ProjectBlock
                        key={project.id}
                        project={project}
                        onClick={() => onProjectClick?.(project)}
                        isHighlighted={highlightProject(project)}
                        size={project.size || 'medium'}
                      />
                    ))}
                  </div>

                  {/* 区域底部装饰 */}
                  <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/50 to-transparent" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 底部硬件层 - 重新设计 */}
      <div className="mt-12">
        {/* 硬件层标题 */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white px-6 py-3 rounded-full shadow-lg">
            <Server className="w-5 h-5" />
            <span className="font-bold">硬件基础设施层</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 服务器 */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex justify-center mb-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                <Server className="w-6 h-6 text-white" />
              </div>
            </div>
            <div className="text-lg font-bold text-blue-800 mb-3">服务器</div>
            <div className="flex justify-center space-x-2">
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105">
                <div className="flex items-center space-x-1">
                  <Cpu className="w-3 h-3" />
                  <span>Intel</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-red-600 to-red-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105">
                <div className="flex items-center space-x-1">
                  <Cpu className="w-3 h-3" />
                  <span>AMD</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-green-600 to-green-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105">
                <div className="flex items-center space-x-1">
                  <Cpu className="w-3 h-3" />
                  <span>ARM</span>
                </div>
              </div>
            </div>
          </div>

          {/* 存储设备 */}
          <div className="bg-gradient-to-br from-purple-50 to-violet-100 border-2 border-purple-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex justify-center mb-3">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center shadow-lg">
                <HardDrive className="w-6 h-6 text-white" />
              </div>
            </div>
            <div className="text-lg font-bold text-purple-800 mb-3">集中式存储设备</div>
            <div className="space-y-2">
              <div className="bg-gradient-to-r from-purple-600 to-violet-700 text-white text-xs px-3 py-2 rounded-full shadow-md">
                网络存储
              </div>
              <div className="bg-gradient-to-r from-purple-600 to-violet-700 text-white text-xs px-3 py-2 rounded-full shadow-md">
                SAN存储
              </div>
            </div>
          </div>

          {/* GPU */}
          <div className="bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div className="flex justify-center mb-3">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-full flex items-center justify-center shadow-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
            </div>
            <div className="text-lg font-bold text-yellow-800 mb-3">GPU加速</div>
            <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white text-sm px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
              <div className="flex items-center justify-center space-x-2">
                <Zap className="w-4 h-4" />
                <span className="font-bold">NVIDIA全系列</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
