'use client'

import { getMaturityColor, getMaturityLabel } from '@/lib/utils'

interface MaturityFilterProps {
  selectedMaturity: string[]
  onMaturityChange: (maturity: string[]) => void
}

const maturityLevels = ['graduated', 'incubating', 'sandbox', 'archived']

export function MaturityFilter({ selectedMaturity, onMaturityChange }: MaturityFilterProps) {
  const toggleMaturity = (maturity: string) => {
    if (selectedMaturity.includes(maturity)) {
      onMaturityChange(selectedMaturity.filter(m => m !== maturity))
    } else {
      onMaturityChange([...selectedMaturity, maturity])
    }
  }

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">成熟度筛选</h3>
      <div className="space-y-2">
        {maturityLevels.map((maturity) => (
          <label key={maturity} className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={selectedMaturity.includes(maturity)}
              onChange={() => toggleMaturity(maturity)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
            />
            <span className={`px-2 py-1 rounded text-xs font-medium ${getMaturityColor(maturity)}`}>
              {getMaturityLabel(maturity)}
            </span>
          </label>
        ))}
      </div>
    </div>
  )
}
