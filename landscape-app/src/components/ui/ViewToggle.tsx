'use client'

import { ViewMode } from '@/types/landscape'
import { Grid3X3, Network } from 'lucide-react'

interface ViewToggleProps {
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
}

export function ViewToggle({ viewMode, onViewModeChange }: ViewToggleProps) {
  return (
    <div className="flex bg-white/80 backdrop-blur-md rounded-xl p-1 shadow-lg border border-white/30">
      <button
        onClick={() => onViewModeChange({ type: 'grid' })}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 ${
          viewMode.type === 'grid'
            ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-105'
            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
        }`}
      >
        <Grid3X3 className="w-4 h-4" />
        <span>网格视图</span>
      </button>

      <button
        onClick={() => onViewModeChange({ type: 'architecture' })}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 ${
          viewMode.type === 'architecture'
            ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white shadow-lg transform scale-105'
            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
        }`}
      >
        <Network className="w-4 h-4" />
        <span>架构图</span>
      </button>
    </div>
  )
}
