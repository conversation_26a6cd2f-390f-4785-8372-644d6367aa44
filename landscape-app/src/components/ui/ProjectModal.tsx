'use client'

import { Project } from '@/types/landscape'
import { formatNumber, getMaturityColor, getMaturityLabel } from '@/lib/utils'
import { ExternalLink, Github, Star, Users, X } from 'lucide-react'

interface ProjectModalProps {
  project: Project | null
  onClose: () => void
}

export function ProjectModal({ project, onClose }: ProjectModalProps) {
  if (!project) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 头部 */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                <span className="text-3xl font-bold text-gray-600">
                  {project.name.charAt(0)}
                </span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{project.name}</h2>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getMaturityColor(project.maturity)}`}>
                  {getMaturityLabel(project.maturity)}
                </span>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* 描述 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">项目描述</h3>
            <p className="text-gray-600 leading-relaxed">{project.description}</p>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {project.stars && (
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="flex items-center justify-center space-x-1 text-yellow-600 mb-1">
                  <Star className="w-4 h-4" />
                  <span className="font-semibold">{formatNumber(project.stars)}</span>
                </div>
                <div className="text-xs text-gray-500">Stars</div>
              </div>
            )}
            {project.contributors && (
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                  <Users className="w-4 h-4" />
                  <span className="font-semibold">{formatNumber(project.contributors)}</span>
                </div>
                <div className="text-xs text-gray-500">贡献者</div>
              </div>
            )}
            {project.license && (
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="font-semibold text-gray-900 mb-1">{project.license}</div>
                <div className="text-xs text-gray-500">许可证</div>
              </div>
            )}
            {project.language && (
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="font-semibold text-gray-900 mb-1">{project.language}</div>
                <div className="text-xs text-gray-500">主要语言</div>
              </div>
            )}
          </div>

          {/* 标签 */}
          {project.tags.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">标签</h3>
              <div className="flex flex-wrap gap-2">
                {project.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 链接 */}
          <div className="flex space-x-4">
            {project.homepage_url && (
              <a
                href={project.homepage_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ExternalLink className="w-4 h-4" />
                <span>访问官网</span>
              </a>
            )}
            {project.github_url && (
              <a
                href={project.github_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
              >
                <Github className="w-4 h-4" />
                <span>查看源码</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
