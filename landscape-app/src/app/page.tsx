'use client'

import { useState, useEffect, useMemo } from 'react'
import { Project, Category, LandscapeData, FilterState, ViewMode } from '@/types/landscape'
import { ProjectCard } from '@/components/ui/ProjectCard'
import { SearchBar } from '@/components/ui/SearchBar'
import { CategoryFilter } from '@/components/ui/CategoryFilter'
import { MaturityFilter } from '@/components/ui/MaturityFilter'
import { ArchitectureDiagram } from '@/components/ui/ArchitectureDiagram'
import { ViewToggle } from '@/components/ui/ViewToggle'
import { ProjectModal } from '@/components/ui/ProjectModal'
import { Filter, X } from 'lucide-react'

export default function Home() {
  const [data, setData] = useState<LandscapeData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<ViewMode>({ type: 'architecture' })
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    maturity: [],
    tags: [],
    layer: ''
  })

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/landscape.json')
        const landscapeData: LandscapeData = await response.json()
        setData(landscapeData)
      } catch (error) {
        console.error('Failed to load landscape data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const filteredProjects = useMemo(() => {
    if (!data) return []

    return data.projects.filter(project => {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const matchesSearch = 
          project.name.toLowerCase().includes(searchTerm) ||
          project.description.toLowerCase().includes(searchTerm) ||
          project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        
        if (!matchesSearch) return false
      }

      if (filters.category && project.category !== filters.category) {
        return false
      }

      if (filters.maturity.length > 0 && !filters.maturity.includes(project.maturity)) {
        return false
      }

      return true
    })
  }, [data, filters])

  const stats = useMemo(() => {
    if (!data) return { total: 0, categories: 0, graduated: 0 }

    const graduated = data.projects.filter(p => p.maturity === 'graduated').length
    
    return {
      total: data.projects.length,
      categories: data.categories.length,
      graduated
    }
  }, [data])

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      maturity: [],
      tags: [],
      layer: ''
    })
  }

  const hasActiveFilters = filters.search || filters.category || filters.maturity.length > 0 || filters.layer

  const handleProjectClick = (project: Project) => {
    setSelectedProject(project)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">加载数据失败</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4 animate-float">
              云原生技术全景图
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              探索云原生生态系统中的开源项目和工具，构建现代化的技术架构
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg">{stats.total}</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-blue-600 text-center">{stats.total}</div>
                <div className="text-sm text-blue-800 text-center font-medium">总项目数</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg">{stats.categories}</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-green-600 text-center">{stats.categories}</div>
                <div className="text-sm text-green-800 text-center font-medium">分类数</div>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-violet-100 border-2 border-purple-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-lg">{stats.graduated}</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-purple-600 text-center">{stats.graduated}</div>
                <div className="text-sm text-purple-800 text-center font-medium">已毕业项目</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 space-y-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1 max-w-md">
              <SearchBar
                value={filters.search}
                onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
                placeholder="搜索项目、描述或标签..."
              />
            </div>
            <div className="flex gap-2 items-center">
              <ViewToggle
                viewMode={viewMode}
                onViewModeChange={setViewMode}
              />
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Filter className="w-4 h-4" />
                高级筛选
              </button>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                >
                  <X className="w-4 h-4" />
                  清除筛选
                </button>
              )}
            </div>
          </div>

          {viewMode.type === 'grid' && (
            <div className="flex justify-center">
              <CategoryFilter
                categories={data.categories}
                selectedCategory={filters.category}
                onCategoryChange={(categoryId) => setFilters(prev => ({ ...prev, category: categoryId }))}
              />
            </div>
          )}

          {showFilters && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <MaturityFilter
                selectedMaturity={filters.maturity}
                onMaturityChange={(maturity) => setFilters(prev => ({ ...prev, maturity }))}
              />
            </div>
          )}
        </div>

        {viewMode.type === 'grid' && (
          <div className="mb-6">
            <p className="text-gray-600 text-center">
              显示 {filteredProjects.length} 个项目
              {filters.search && ` (搜索: "${filters.search}")`}
              {filters.category && ` (分类: ${data.categories.find(c => c.id === filters.category)?.name})`}
            </p>
          </div>
        )}

        {viewMode.type === 'architecture' ? (
          <div className="architecture-container">
            <ArchitectureDiagram
              layers={data.architectureLayers || []}
              onProjectClick={handleProjectClick}
              searchTerm={filters.search}
            />
          </div>
        ) : filteredProjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">没有找到匹配的项目</p>
            <p className="text-gray-400 text-sm mt-2">尝试调整搜索条件或过滤器</p>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                清除所有筛选条件
              </button>
            )}
          </div>
        )}
      </main>

      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>&copy; 2024 云原生技术全景图. 基于开源项目构建.</p>
          </div>
        </div>
      </footer>

      {/* 项目详情模态框 */}
      <ProjectModal
        project={selectedProject}
        onClose={() => setSelectedProject(null)}
      />
    </div>
  )
}
