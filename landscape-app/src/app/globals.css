@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer components {
  /* 自定义动画 */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* 响应式架构图 */
  .architecture-container {
    @apply w-full overflow-x-auto;
  }

  .architecture-layer {
    @apply min-w-[800px];
  }

  @screen lg {
    .architecture-layer {
      @apply min-w-0;
    }
  }

  .architecture-section {
    @apply min-w-[200px] sm:min-w-[250px] lg:min-w-[300px];
  }

  /* 项目块悬停效果 */
  .project-block {
    @apply relative overflow-hidden;
  }

  .project-block::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    @apply transform -translate-x-full transition-transform duration-700;
  }

  .project-block:hover::before {
    @apply translate-x-full;
  }

  /* 玻璃态效果 */
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* 渐变边框 */
  .gradient-border {
    @apply relative;
  }

  .gradient-border::before {
    content: '';
    @apply absolute inset-0 rounded-xl;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ef4444, #f59e0b);
    @apply -z-10;
    padding: 2px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}
