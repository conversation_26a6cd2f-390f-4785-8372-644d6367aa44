/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeHV5YW5nYm8lMkZnaXRodWIuY29tJTJGbGFuZHNjYXBlJTJGbGFuZHNjYXBlLWFwcCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"35ff643e7b67\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMveHV5YW5nYm8vZ2l0aHViLmNvbS9sYW5kc2NhcGUvbGFuZHNjYXBlLWFwcC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzVmZjY0M2U3YjY3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeHV5YW5nYm8lMkZnaXRodWIuY29tJTJGbGFuZHNjYXBlJTJGbGFuZHNjYXBlLWFwcCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_ProjectCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ProjectCard */ \"(ssr)/./src/components/ui/ProjectCard.tsx\");\n/* harmony import */ var _components_ui_SearchBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/SearchBar */ \"(ssr)/./src/components/ui/SearchBar.tsx\");\n/* harmony import */ var _components_ui_CategoryFilter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/CategoryFilter */ \"(ssr)/./src/components/ui/CategoryFilter.tsx\");\n/* harmony import */ var _components_ui_MaturityFilter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/MaturityFilter */ \"(ssr)/./src/components/ui/MaturityFilter.tsx\");\n/* harmony import */ var _components_ui_ArchitectureDiagram__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ArchitectureDiagram */ \"(ssr)/./src/components/ui/ArchitectureDiagram.tsx\");\n/* harmony import */ var _components_ui_ViewToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ViewToggle */ \"(ssr)/./src/components/ui/ViewToggle.tsx\");\n/* harmony import */ var _components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ProjectModal */ \"(ssr)/./src/components/ui/ProjectModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction Home() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'architecture'\n    });\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        category: '',\n        maturity: [],\n        tags: [],\n        layer: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadData = {\n                \"Home.useEffect.loadData\": async ()=>{\n                    try {\n                        const response = await fetch('/data/landscape.json');\n                        const landscapeData = await response.json();\n                        setData(landscapeData);\n                    } catch (error) {\n                        console.error('Failed to load landscape data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"Home.useEffect\"], []);\n    const filteredProjects = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[filteredProjects]\": ()=>{\n            if (!data) return [];\n            return data.projects.filter({\n                \"Home.useMemo[filteredProjects]\": (project)=>{\n                    if (filters.search) {\n                        const searchTerm = filters.search.toLowerCase();\n                        const matchesSearch = project.name.toLowerCase().includes(searchTerm) || project.description.toLowerCase().includes(searchTerm) || project.tags.some({\n                            \"Home.useMemo[filteredProjects]\": (tag)=>tag.toLowerCase().includes(searchTerm)\n                        }[\"Home.useMemo[filteredProjects]\"]);\n                        if (!matchesSearch) return false;\n                    }\n                    if (filters.category && project.category !== filters.category) {\n                        return false;\n                    }\n                    if (filters.maturity.length > 0 && !filters.maturity.includes(project.maturity)) {\n                        return false;\n                    }\n                    return true;\n                }\n            }[\"Home.useMemo[filteredProjects]\"]);\n        }\n    }[\"Home.useMemo[filteredProjects]\"], [\n        data,\n        filters\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[stats]\": ()=>{\n            if (!data) return {\n                total: 0,\n                categories: 0,\n                graduated: 0\n            };\n            const graduated = data.projects.filter({\n                \"Home.useMemo[stats]\": (p)=>p.maturity === 'graduated'\n            }[\"Home.useMemo[stats]\"]).length;\n            return {\n                total: data.projects.length,\n                categories: data.categories.length,\n                graduated\n            };\n        }\n    }[\"Home.useMemo[stats]\"], [\n        data\n    ]);\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            category: '',\n            maturity: [],\n            tags: [],\n            layer: ''\n        });\n    };\n    const hasActiveFilters = filters.search || filters.category || filters.maturity.length > 0 || filters.layer;\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    if (!data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"加载数据失败\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4 animate-float\",\n                                children: \"云原生技术全景图\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg md:text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"探索云原生生态系统中的开源项目和工具，构建现代化的技术架构\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: stats.total\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600 text-center\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-800 text-center font-medium\",\n                                                children: \"总项目数\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: stats.categories\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600 text-center\",\n                                                children: stats.categories\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-800 text-center font-medium\",\n                                                children: \"分类数\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-50 to-violet-100 border-2 border-purple-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: stats.graduated\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600 text-center\",\n                                                children: stats.graduated\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-800 text-center font-medium\",\n                                                children: \"已毕业项目\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchBar__WEBPACK_IMPORTED_MODULE_3__.SearchBar, {\n                                            value: filters.search,\n                                            onChange: (value)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        search: value\n                                                    })),\n                                            placeholder: \"搜索项目、描述或标签...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ViewToggle__WEBPACK_IMPORTED_MODULE_7__.ViewToggle, {\n                                                viewMode: viewMode,\n                                                onViewModeChange: setViewMode\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowFilters(!showFilters),\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"高级筛选\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearFilters,\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"清除筛选\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            viewMode.type === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CategoryFilter__WEBPACK_IMPORTED_MODULE_4__.CategoryFilter, {\n                                    categories: data.categories,\n                                    selectedCategory: filters.category,\n                                    onCategoryChange: (categoryId)=>setFilters((prev)=>({\n                                                ...prev,\n                                                category: categoryId\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_MaturityFilter__WEBPACK_IMPORTED_MODULE_5__.MaturityFilter, {\n                                    selectedMaturity: filters.maturity,\n                                    onMaturityChange: (maturity)=>setFilters((prev)=>({\n                                                ...prev,\n                                                maturity\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    viewMode.type === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-center\",\n                            children: [\n                                \"显示 \",\n                                filteredProjects.length,\n                                \" 个项目\",\n                                filters.search && ` (搜索: \"${filters.search}\")`,\n                                filters.category && ` (分类: ${data.categories.find((c)=>c.id === filters.category)?.name})`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    viewMode.type === 'architecture' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"architecture-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ArchitectureDiagram__WEBPACK_IMPORTED_MODULE_6__.ArchitectureDiagram, {\n                            layers: data.architectureLayers || [],\n                            onProjectClick: handleProjectClick,\n                            searchTerm: filters.search\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this) : filteredProjects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                        children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectCard__WEBPACK_IMPORTED_MODULE_2__.ProjectCard, {\n                                project: project\n                            }, project.id, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"没有找到匹配的项目\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mt-2\",\n                                children: \"尝试调整搜索条件或过滤器\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearFilters,\n                                className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"清除所有筛选条件\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 云原生技术全景图. 基于开源项目构建.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_8__.ProjectModal, {\n                project: selectedProject,\n                onClose: ()=>setSelectedProject(null)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ArchitectureDiagram.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ArchitectureDiagram.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchitectureDiagram: () => (/* binding */ ArchitectureDiagram)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ProjectBlock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProjectBlock */ \"(ssr)/./src/components/ui/ProjectBlock.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Cpu,HardDrive,Layers,Server,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ ArchitectureDiagram auto */ \n\n\nfunction ArchitectureDiagram({ layers, onProjectClick, searchTerm }) {\n    const sortedLayers = [\n        ...layers\n    ].sort((a, b)=>a.order - b.order);\n    const highlightProject = (project)=>{\n        if (!searchTerm) return false;\n        const term = searchTerm.toLowerCase();\n        return project.name.toLowerCase().includes(term) || project.description.toLowerCase().includes(term) || project.tags.some((tag)=>tag.toLowerCase().includes(term));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-gradient-to-br from-slate-50 to-blue-50 p-8 rounded-2xl border border-slate-200 shadow-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-white/50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-6 h-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-800\",\n                            children: \"云原生技术架构全景图\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: sortedLayers.map((layer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-px h-4 bg-gradient-to-b from-gray-300 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative rounded-2xl border-2 p-6 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300\",\n                                style: {\n                                    background: `linear-gradient(135deg, ${layer.backgroundColor}, ${layer.backgroundColor}dd)`,\n                                    borderColor: layer.borderColor,\n                                    minHeight: `${layer.height}px`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-4 left-6 px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white/50\",\n                                        style: {\n                                            background: `linear-gradient(135deg, ${layer.color}, ${layer.color}dd)`,\n                                            color: 'white'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-white/80 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: layer.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex flex-wrap gap-6 min-h-[140px]\",\n                                        children: layer.sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative rounded-xl border-2 p-5 flex-1 min-w-[220px] backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300\",\n                                                style: {\n                                                    background: `linear-gradient(135deg, ${section.backgroundColor || 'rgba(255, 255, 255, 0.9)'}, ${section.backgroundColor || 'rgba(255, 255, 255, 0.7)'})`,\n                                                    borderColor: section.borderColor || '#e5e7eb',\n                                                    width: `${section.width * 100}%`\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent rounded-t-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-bold text-gray-800 mb-4 text-center bg-white/50 backdrop-blur-sm rounded-lg py-2 px-3 border border-white/30\",\n                                                        children: section.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3 justify-center\",\n                                                        children: section.projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectBlock__WEBPACK_IMPORTED_MODULE_1__.ProjectBlock, {\n                                                                project: project,\n                                                                onClick: ()=>onProjectClick?.(project),\n                                                                isHighlighted: highlightProject(project),\n                                                                size: project.size || 'medium'\n                                                            }, project.id, false, {\n                                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/50 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, section.id, true, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, layer.id, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white px-6 py-3 rounded-full shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"硬件基础设施层\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-blue-800 mb-3\",\n                                        children: \"服务器\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Intel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-600 to-red-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"AMD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white text-xs px-3 py-2 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"ARM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-50 to-violet-100 border-2 border-purple-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-purple-800 mb-3\",\n                                        children: \"集中式存储设备\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-purple-600 to-violet-700 text-white text-xs px-3 py-2 rounded-full shadow-md\",\n                                                children: \"网络存储\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-purple-600 to-violet-700 text-white text-xs px-3 py-2 rounded-full shadow-md\",\n                                                children: \"SAN存储\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-200 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-full flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-yellow-800 mb-3\",\n                                        children: \"GPU加速\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-green-600 to-emerald-700 text-white text-sm px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Cpu_HardDrive_Layers_Server_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: \"NVIDIA全系列\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ArchitectureDiagram.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/CategoryFilter.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/CategoryFilter.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryFilter: () => (/* binding */ CategoryFilter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ CategoryFilter auto */ \nfunction CategoryFilter({ categories, selectedCategory, onCategoryChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onCategoryChange(''),\n                className: `px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedCategory === '' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                children: \"全部\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCategoryChange(category.id),\n                    className: `px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedCategory === category.id ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    style: {\n                        backgroundColor: selectedCategory === category.id ? category.color : undefined\n                    },\n                    children: category.name\n                }, category.id, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/CategoryFilter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/MaturityFilter.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/MaturityFilter.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaturityFilter: () => (/* binding */ MaturityFilter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MaturityFilter auto */ \n\nconst maturityLevels = [\n    'graduated',\n    'incubating',\n    'sandbox',\n    'archived'\n];\nfunction MaturityFilter({ selectedMaturity, onMaturityChange }) {\n    const toggleMaturity = (maturity)=>{\n        if (selectedMaturity.includes(maturity)) {\n            onMaturityChange(selectedMaturity.filter((m)=>m !== maturity));\n        } else {\n            onMaturityChange([\n                ...selectedMaturity,\n                maturity\n            ]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"成熟度筛选\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: maturityLevels.map((maturity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: selectedMaturity.includes(maturity),\n                                onChange: ()=>toggleMaturity(maturity),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(maturity)}`,\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(maturity)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, maturity, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/MaturityFilter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectBlock.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProjectBlock.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectBlock: () => (/* binding */ ProjectBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _lib_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/icons */ \"(ssr)/./src/lib/icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectBlock auto */ \n\n\nfunction ProjectBlock({ project, onClick, isHighlighted, size = 'medium' }) {\n    const IconComponent = (0,_lib_icons__WEBPACK_IMPORTED_MODULE_1__.getProjectIcon)(project.id);\n    const sizeClasses = {\n        small: 'px-3 py-2 text-xs min-w-[80px] h-10',\n        medium: 'px-4 py-3 text-sm min-w-[100px] h-12',\n        large: 'px-5 py-4 text-base min-w-[140px] h-16'\n    };\n    const iconSizes = {\n        small: 'w-3 h-3',\n        medium: 'w-4 h-4',\n        large: 'w-5 h-5'\n    };\n    const getMaturityGradient = (maturity)=>{\n        switch(maturity){\n            case 'graduated':\n                return 'bg-gradient-to-br from-emerald-500 to-green-600 border-emerald-400 text-white shadow-emerald-200';\n            case 'incubating':\n                return 'bg-gradient-to-br from-blue-500 to-indigo-600 border-blue-400 text-white shadow-blue-200';\n            case 'sandbox':\n                return 'bg-gradient-to-br from-amber-500 to-orange-600 border-amber-400 text-white shadow-amber-200';\n            case 'archived':\n                return 'bg-gradient-to-br from-gray-500 to-slate-600 border-gray-400 text-white shadow-gray-200';\n            default:\n                return 'bg-gradient-to-br from-gray-400 to-gray-500 border-gray-300 text-white shadow-gray-200';\n        }\n    };\n    const baseClasses = `\n    relative rounded-xl border-2 cursor-pointer transition-all duration-300 ease-out\n    flex flex-col items-center justify-center text-center font-semibold\n    hover:shadow-xl hover:scale-105 hover:-translate-y-1 group\n    backdrop-blur-sm\n    ${sizeClasses[size]}\n    ${isHighlighted ? 'ring-4 ring-yellow-400 ring-offset-2 ring-offset-white' : ''}\n    ${project.color ? 'text-white shadow-lg' : getMaturityGradient(project.maturity)}\n  `;\n    const customStyle = project.color ? {\n        background: `linear-gradient(135deg, ${project.color}, ${project.color}dd)`,\n        borderColor: project.color,\n        boxShadow: `0 10px 25px -5px ${project.color}40, 0 4px 6px -2px ${project.color}20`\n    } : {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        style: customStyle,\n        onClick: onClick,\n        title: `${project.name} - ${project.description}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-xl bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mb-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                    className: `${iconSizes[size]} opacity-90`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"truncate text-center leading-tight\",\n                children: project.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-3 -right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-100 scale-75 flex space-x-1\",\n                children: [\n                    project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: project.homepage_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"w-6 h-6 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-200\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-3 h-3 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: project.github_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"w-6 h-6 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-200\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-3 h-3 text-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-2 -right-2 w-4 h-4 rounded-full border-2 border-white shadow-lg\",\n                style: {\n                    backgroundColor: project.maturity === 'graduated' ? '#10b981' : project.maturity === 'incubating' ? '#3b82f6' : project.maturity === 'sandbox' ? '#f59e0b' : '#6b7280'\n                },\n                title: `成熟度: ${project.maturity}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0.5 rounded-full bg-white/30\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-xl bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectBlock.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ProjectCard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectCard: () => (/* binding */ ProjectCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectCard auto */ \n\n\nfunction ProjectCard({ project }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: project.name.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: project.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(project.maturity)}`,\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(project.maturity)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.homepage_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.github_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm mb-4 line-clamp-3\",\n                children: project.description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 mb-4\",\n                children: [\n                    project.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                            children: tag\n                        }, tag, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)),\n                    project.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                        children: [\n                            \"+\",\n                            project.tags.length - 3\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            project.stars && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.stars)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            project.contributors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.contributors)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    project.language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                        children: project.language\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectModal.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProjectModal.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectModal: () => (/* binding */ ProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectModal auto */ \n\n\nfunction ProjectModal({ project, onClose }) {\n    if (!project) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl font-bold text-gray-600\",\n                                            children: project.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: project.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(project.maturity)}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(project.maturity)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"项目描述\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            project.stars && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-yellow-600 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.stars)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Stars\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            project.contributors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-blue-600 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.contributors)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"贡献者\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this),\n                            project.license && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-gray-900 mb-1\",\n                                        children: project.license\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"许可证\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            project.language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-gray-900 mb-1\",\n                                        children: project.language\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"主要语言\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    project.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                children: \"标签\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: project.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                        children: tag\n                                    }, tag, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.homepage_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"访问官网\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.github_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"查看源码\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/SearchBar.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/SearchBar.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ SearchBar auto */ \n\nfunction SearchBar({ value, onChange, placeholder = \"搜索项目...\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: placeholder\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ViewToggle.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/ViewToggle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewToggle: () => (/* binding */ ViewToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Network!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Network!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* __next_internal_client_entry_do_not_use__ ViewToggle auto */ \n\nfunction ViewToggle({ viewMode, onViewModeChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex bg-white/80 backdrop-blur-md rounded-xl p-1 shadow-lg border border-white/30\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onViewModeChange({\n                        type: 'grid'\n                    }),\n                className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 ${viewMode.type === 'grid' ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-105' : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"网格视图\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onViewModeChange({\n                        type: 'architecture'\n                    }),\n                className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 ${viewMode.type === 'architecture' ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white shadow-lg transform scale-105' : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"架构图\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ViewToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/icons.tsx":
/*!***************************!*\
  !*** ./src/lib/icons.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryIcons: () => (/* binding */ categoryIcons),\n/* harmony export */   getCategoryIcon: () => (/* binding */ getCategoryIcon),\n/* harmony export */   getHardwareIcon: () => (/* binding */ getHardwareIcon),\n/* harmony export */   getProjectIcon: () => (/* binding */ getProjectIcon),\n/* harmony export */   hardwareIcons: () => (/* binding */ hardwareIcons),\n/* harmony export */   projectIcons: () => (/* binding */ projectIcons)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/box.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/git-branch.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Box,Cloud,Cog,Container,Cpu,Database,Eye,Gauge,GitBranch,HardDrive,Layers,Monitor,Network,Server,Settings,Shield,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n\n// 技术类别图标映射\nconst categoryIcons = {\n    'container-runtime': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    'orchestration': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    'observability': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    'security': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    'storage': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    'networking': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    'compute': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    'platform': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    'monitoring': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    'management': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\n// 项目特定图标映射\nconst projectIcons = {\n    'docker': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    'kubernetes': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    'prometheus': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    'grafana': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    'containerd': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    'helm': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    'istio': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    'etcd': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    'falco': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    'jaeger': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    'zstack-zaku': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    'zstack-rds': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    'zstack-sds': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    'multi-tenant': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    'sdn': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    'cmp': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    'compute-mgmt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    'monitoring': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    'vmware-mgmt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    'zsphere-mgmt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    'kvm-virt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    'network-virt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    'storage-virt': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n};\n// 硬件类型图标\nconst hardwareIcons = {\n    'intel': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    'amd': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    'arm': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    'nvidia': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    'storage': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    'server': _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n};\n// 获取项目图标\nfunction getProjectIcon(projectId) {\n    return projectIcons[projectId] || _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n}\n// 获取分类图标\nfunction getCategoryIcon(categoryId) {\n    return categoryIcons[categoryId] || _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"];\n}\n// 获取硬件图标\nfunction getHardwareIcon(hardwareType) {\n    return hardwareIcons[hardwareType] || _barrel_optimize_names_Activity_BarChart3_Box_Cloud_Cog_Container_Cpu_Database_Eye_Gauge_GitBranch_HardDrive_Layers_Monitor_Network_Server_Settings_Shield_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/icons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getMaturityColor: () => (/* binding */ getMaturityColor),\n/* harmony export */   getMaturityLabel: () => (/* binding */ getMaturityLabel)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\nfunction getMaturityColor(maturity) {\n    switch(maturity){\n        case 'graduated':\n            return 'bg-green-100 text-green-800 border-green-200';\n        case 'incubating':\n            return 'bg-blue-100 text-blue-800 border-blue-200';\n        case 'sandbox':\n            return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        case 'archived':\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n        default:\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n}\nfunction getMaturityLabel(maturity) {\n    switch(maturity){\n        case 'graduated':\n            return '已毕业';\n        case 'incubating':\n            return '孵化中';\n        case 'sandbox':\n            return '沙盒';\n        case 'archived':\n            return '已归档';\n        default:\n            return maturity;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();