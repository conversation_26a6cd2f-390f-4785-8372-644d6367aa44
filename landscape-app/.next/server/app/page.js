/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeHV5YW5nYm8lMkZnaXRodWIuY29tJTJGbGFuZHNjYXBlJTJGbGFuZHNjYXBlLWFwcCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"003192d12909\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMveHV5YW5nYm8vZ2l0aHViLmNvbS9sYW5kc2NhcGUvbGFuZHNjYXBlLWFwcC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDAzMTkyZDEyOTA5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeHV5YW5nYm8lMkZnaXRodWIuY29tJTJGbGFuZHNjYXBlJTJGbGFuZHNjYXBlLWFwcCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_ProjectCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ProjectCard */ \"(ssr)/./src/components/ui/ProjectCard.tsx\");\n/* harmony import */ var _components_ui_SearchBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/SearchBar */ \"(ssr)/./src/components/ui/SearchBar.tsx\");\n/* harmony import */ var _components_ui_CategoryFilter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/CategoryFilter */ \"(ssr)/./src/components/ui/CategoryFilter.tsx\");\n/* harmony import */ var _components_ui_MaturityFilter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/MaturityFilter */ \"(ssr)/./src/components/ui/MaturityFilter.tsx\");\n/* harmony import */ var _components_ui_ArchitectureDiagram__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ArchitectureDiagram */ \"(ssr)/./src/components/ui/ArchitectureDiagram.tsx\");\n/* harmony import */ var _components_ui_ViewToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ViewToggle */ \"(ssr)/./src/components/ui/ViewToggle.tsx\");\n/* harmony import */ var _components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ProjectModal */ \"(ssr)/./src/components/ui/ProjectModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction Home() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'architecture'\n    });\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        category: '',\n        maturity: [],\n        tags: [],\n        layer: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const loadData = {\n                \"Home.useEffect.loadData\": async ()=>{\n                    try {\n                        const response = await fetch('/data/landscape.json');\n                        const landscapeData = await response.json();\n                        setData(landscapeData);\n                    } catch (error) {\n                        console.error('Failed to load landscape data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"Home.useEffect\"], []);\n    const filteredProjects = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[filteredProjects]\": ()=>{\n            if (!data) return [];\n            return data.projects.filter({\n                \"Home.useMemo[filteredProjects]\": (project)=>{\n                    if (filters.search) {\n                        const searchTerm = filters.search.toLowerCase();\n                        const matchesSearch = project.name.toLowerCase().includes(searchTerm) || project.description.toLowerCase().includes(searchTerm) || project.tags.some({\n                            \"Home.useMemo[filteredProjects]\": (tag)=>tag.toLowerCase().includes(searchTerm)\n                        }[\"Home.useMemo[filteredProjects]\"]);\n                        if (!matchesSearch) return false;\n                    }\n                    if (filters.category && project.category !== filters.category) {\n                        return false;\n                    }\n                    if (filters.maturity.length > 0 && !filters.maturity.includes(project.maturity)) {\n                        return false;\n                    }\n                    return true;\n                }\n            }[\"Home.useMemo[filteredProjects]\"]);\n        }\n    }[\"Home.useMemo[filteredProjects]\"], [\n        data,\n        filters\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[stats]\": ()=>{\n            if (!data) return {\n                total: 0,\n                categories: 0,\n                graduated: 0\n            };\n            const graduated = data.projects.filter({\n                \"Home.useMemo[stats]\": (p)=>p.maturity === 'graduated'\n            }[\"Home.useMemo[stats]\"]).length;\n            return {\n                total: data.projects.length,\n                categories: data.categories.length,\n                graduated\n            };\n        }\n    }[\"Home.useMemo[stats]\"], [\n        data\n    ]);\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            category: '',\n            maturity: [],\n            tags: [],\n            layer: ''\n        });\n    };\n    const hasActiveFilters = filters.search || filters.category || filters.maturity.length > 0 || filters.layer;\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    if (!data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"加载数据失败\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                children: \"云原生技术全景图\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-8\",\n                                children: \"探索云原生生态系统中的开源项目和工具\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: \"总项目数\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: stats.categories\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-800\",\n                                                children: \"分类数\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: stats.graduated\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-800\",\n                                                children: \"已毕业项目\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchBar__WEBPACK_IMPORTED_MODULE_3__.SearchBar, {\n                                            value: filters.search,\n                                            onChange: (value)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        search: value\n                                                    })),\n                                            placeholder: \"搜索项目、描述或标签...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ViewToggle__WEBPACK_IMPORTED_MODULE_7__.ViewToggle, {\n                                                viewMode: viewMode,\n                                                onViewModeChange: setViewMode\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowFilters(!showFilters),\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"高级筛选\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearFilters,\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"清除筛选\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            viewMode.type === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CategoryFilter__WEBPACK_IMPORTED_MODULE_4__.CategoryFilter, {\n                                    categories: data.categories,\n                                    selectedCategory: filters.category,\n                                    onCategoryChange: (categoryId)=>setFilters((prev)=>({\n                                                ...prev,\n                                                category: categoryId\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_MaturityFilter__WEBPACK_IMPORTED_MODULE_5__.MaturityFilter, {\n                                    selectedMaturity: filters.maturity,\n                                    onMaturityChange: (maturity)=>setFilters((prev)=>({\n                                                ...prev,\n                                                maturity\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    viewMode.type === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-center\",\n                            children: [\n                                \"显示 \",\n                                filteredProjects.length,\n                                \" 个项目\",\n                                filters.search && ` (搜索: \"${filters.search}\")`,\n                                filters.category && ` (分类: ${data.categories.find((c)=>c.id === filters.category)?.name})`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    viewMode.type === 'architecture' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ArchitectureDiagram__WEBPACK_IMPORTED_MODULE_6__.ArchitectureDiagram, {\n                        layers: data.architectureLayers || [],\n                        onProjectClick: handleProjectClick,\n                        searchTerm: filters.search\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this) : filteredProjects.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                        children: filteredProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectCard__WEBPACK_IMPORTED_MODULE_2__.ProjectCard, {\n                                project: project\n                            }, project.id, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"没有找到匹配的项目\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mt-2\",\n                                children: \"尝试调整搜索条件或过滤器\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearFilters,\n                                className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"清除所有筛选条件\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 云原生技术全景图. 基于开源项目构建.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_8__.ProjectModal, {\n                project: selectedProject,\n                onClose: ()=>setSelectedProject(null)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/app/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVvRDtBQUVLO0FBQ0o7QUFDVTtBQUNBO0FBQ1U7QUFDbEI7QUFDSTtBQUNuQjtBQUV6QixTQUFTWTtJQUN0QixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2QsK0NBQVFBLENBQXVCO0lBQ3ZELE1BQU0sQ0FBQ2UsU0FBU0MsV0FBVyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDaUIsYUFBYUMsZUFBZSxHQUFHbEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUIsVUFBVUMsWUFBWSxHQUFHcEIsK0NBQVFBLENBQVc7UUFBRXFCLE1BQU07SUFBZTtJQUMxRSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUd2QiwrQ0FBUUEsQ0FBaUI7SUFDdkUsTUFBTSxDQUFDd0IsU0FBU0MsV0FBVyxHQUFHekIsK0NBQVFBLENBQWM7UUFDbEQwQixRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsVUFBVSxFQUFFO1FBQ1pDLE1BQU0sRUFBRTtRQUNSQyxPQUFPO0lBQ1Q7SUFFQTdCLGdEQUFTQTswQkFBQztZQUNSLE1BQU04QjsyQ0FBVztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTt3QkFDN0IsTUFBTUMsZ0JBQStCLE1BQU1GLFNBQVNHLElBQUk7d0JBQ3hEckIsUUFBUW9CO29CQUNWLEVBQUUsT0FBT0UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7b0JBQ2xELFNBQVU7d0JBQ1JwQixXQUFXO29CQUNiO2dCQUNGOztZQUVBZTtRQUNGO3lCQUFHLEVBQUU7SUFFTCxNQUFNTyxtQkFBbUJwQyw4Q0FBT0E7MENBQUM7WUFDL0IsSUFBSSxDQUFDVyxNQUFNLE9BQU8sRUFBRTtZQUVwQixPQUFPQSxLQUFLMEIsUUFBUSxDQUFDQyxNQUFNO2tEQUFDQyxDQUFBQTtvQkFDMUIsSUFBSWpCLFFBQVFFLE1BQU0sRUFBRTt3QkFDbEIsTUFBTWdCLGFBQWFsQixRQUFRRSxNQUFNLENBQUNpQixXQUFXO3dCQUM3QyxNQUFNQyxnQkFDSkgsUUFBUUksSUFBSSxDQUFDRixXQUFXLEdBQUdHLFFBQVEsQ0FBQ0osZUFDcENELFFBQVFNLFdBQVcsQ0FBQ0osV0FBVyxHQUFHRyxRQUFRLENBQUNKLGVBQzNDRCxRQUFRWixJQUFJLENBQUNtQixJQUFJOzhEQUFDQyxDQUFBQSxNQUFPQSxJQUFJTixXQUFXLEdBQUdHLFFBQVEsQ0FBQ0o7O3dCQUV0RCxJQUFJLENBQUNFLGVBQWUsT0FBTztvQkFDN0I7b0JBRUEsSUFBSXBCLFFBQVFHLFFBQVEsSUFBSWMsUUFBUWQsUUFBUSxLQUFLSCxRQUFRRyxRQUFRLEVBQUU7d0JBQzdELE9BQU87b0JBQ1Q7b0JBRUEsSUFBSUgsUUFBUUksUUFBUSxDQUFDc0IsTUFBTSxHQUFHLEtBQUssQ0FBQzFCLFFBQVFJLFFBQVEsQ0FBQ2tCLFFBQVEsQ0FBQ0wsUUFBUWIsUUFBUSxHQUFHO3dCQUMvRSxPQUFPO29CQUNUO29CQUVBLE9BQU87Z0JBQ1Q7O1FBQ0Y7eUNBQUc7UUFBQ2Y7UUFBTVc7S0FBUTtJQUVsQixNQUFNMkIsUUFBUWpELDhDQUFPQTsrQkFBQztZQUNwQixJQUFJLENBQUNXLE1BQU0sT0FBTztnQkFBRXVDLE9BQU87Z0JBQUdDLFlBQVk7Z0JBQUdDLFdBQVc7WUFBRTtZQUUxRCxNQUFNQSxZQUFZekMsS0FBSzBCLFFBQVEsQ0FBQ0MsTUFBTTt1Q0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRTNCLFFBQVEsS0FBSztzQ0FBYXNCLE1BQU07WUFFOUUsT0FBTztnQkFDTEUsT0FBT3ZDLEtBQUswQixRQUFRLENBQUNXLE1BQU07Z0JBQzNCRyxZQUFZeEMsS0FBS3dDLFVBQVUsQ0FBQ0gsTUFBTTtnQkFDbENJO1lBQ0Y7UUFDRjs4QkFBRztRQUFDekM7S0FBSztJQUVULE1BQU0yQyxlQUFlO1FBQ25CL0IsV0FBVztZQUNUQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsVUFBVSxFQUFFO1lBQ1pDLE1BQU0sRUFBRTtZQUNSQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU0yQixtQkFBbUJqQyxRQUFRRSxNQUFNLElBQUlGLFFBQVFHLFFBQVEsSUFBSUgsUUFBUUksUUFBUSxDQUFDc0IsTUFBTSxHQUFHLEtBQUsxQixRQUFRTSxLQUFLO0lBRTNHLE1BQU00QixxQkFBcUIsQ0FBQ2pCO1FBQzFCbEIsbUJBQW1Ca0I7SUFDckI7SUFFQSxJQUFJMUIsU0FBUztRQUNYLHFCQUNFLDhEQUFDNEM7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDTDt3QkFBRUssV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTFDO0lBRUEsSUFBSSxDQUFDL0MsTUFBTTtRQUNULHFCQUNFLDhEQUFDOEM7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNMO29CQUFFSyxXQUFVOzhCQUFlOzs7Ozs7Ozs7Ozs7Ozs7O0lBSXBDO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFHRixXQUFVOzBDQUF3Qzs7Ozs7OzBDQUd0RCw4REFBQ0w7Z0NBQUVLLFdBQVU7MENBQTZCOzs7Ozs7MENBSTFDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQW9DVCxNQUFNQyxLQUFLOzs7Ozs7MERBQzlELDhEQUFDTztnREFBSUMsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFFekMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQXFDVCxNQUFNRSxVQUFVOzs7Ozs7MERBQ3BFLDhEQUFDTTtnREFBSUMsV0FBVTswREFBeUI7Ozs7Ozs7Ozs7OztrREFFMUMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQXNDVCxNQUFNRyxTQUFTOzs7Ozs7MERBQ3BFLDhEQUFDSztnREFBSUMsV0FBVTswREFBMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT25ELDhEQUFDRztnQkFBS0gsV0FBVTs7a0NBQ2QsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3hELCtEQUFTQTs0Q0FDUjRELE9BQU94QyxRQUFRRSxNQUFNOzRDQUNyQnVDLFVBQVUsQ0FBQ0QsUUFBVXZDLFdBQVd5QyxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUV4QyxRQUFRc0M7b0RBQU07NENBQ2xFRyxhQUFZOzs7Ozs7Ozs7OztrREFHaEIsOERBQUNSO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3BELGlFQUFVQTtnREFDVFcsVUFBVUE7Z0RBQ1ZpRCxrQkFBa0JoRDs7Ozs7OzBEQUVwQiw4REFBQ2lEO2dEQUNDQyxTQUFTLElBQU1wRCxlQUFlLENBQUNEO2dEQUMvQjJDLFdBQVU7O2tFQUVWLDhEQUFDbEQsb0ZBQU1BO3dEQUFDa0QsV0FBVTs7Ozs7O29EQUFZOzs7Ozs7OzRDQUcvQkgsa0NBQ0MsOERBQUNZO2dEQUNDQyxTQUFTZDtnREFDVEksV0FBVTs7a0VBRVYsOERBQUNqRCxxRkFBQ0E7d0RBQUNpRCxXQUFVOzs7Ozs7b0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT2hDekMsU0FBU0UsSUFBSSxLQUFLLHdCQUNqQiw4REFBQ3NDO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDdkQseUVBQWNBO29DQUNiZ0QsWUFBWXhDLEtBQUt3QyxVQUFVO29DQUMzQmtCLGtCQUFrQi9DLFFBQVFHLFFBQVE7b0NBQ2xDNkMsa0JBQWtCLENBQUNDLGFBQWVoRCxXQUFXeUMsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFdkMsVUFBVThDOzRDQUFXOzs7Ozs7Ozs7Ozs0QkFLM0Z4RCw2QkFDQyw4REFBQzBDO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDdEQseUVBQWNBO29DQUNib0Usa0JBQWtCbEQsUUFBUUksUUFBUTtvQ0FDbEMrQyxrQkFBa0IsQ0FBQy9DLFdBQWFILFdBQVd5QyxDQUFBQSxPQUFTO2dEQUFFLEdBQUdBLElBQUk7Z0RBQUV0Qzs0Q0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTS9FVCxTQUFTRSxJQUFJLEtBQUssd0JBQ2pCLDhEQUFDc0M7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNMOzRCQUFFSyxXQUFVOztnQ0FBNEI7Z0NBQ25DdEIsaUJBQWlCWSxNQUFNO2dDQUFDO2dDQUMzQjFCLFFBQVFFLE1BQU0sSUFBSSxDQUFDLE9BQU8sRUFBRUYsUUFBUUUsTUFBTSxDQUFDLEVBQUUsQ0FBQztnQ0FDOUNGLFFBQVFHLFFBQVEsSUFBSSxDQUFDLE1BQU0sRUFBRWQsS0FBS3dDLFVBQVUsQ0FBQ3VCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLdEQsUUFBUUcsUUFBUSxHQUFHa0IsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7Ozs7OztvQkFLaEcxQixTQUFTRSxJQUFJLEtBQUssK0JBQ2pCLDhEQUFDZCxtRkFBbUJBO3dCQUNsQndFLFFBQVFsRSxLQUFLbUUsa0JBQWtCLElBQUksRUFBRTt3QkFDckNDLGdCQUFnQnZCO3dCQUNoQmhCLFlBQVlsQixRQUFRRSxNQUFNOzs7OzsrQkFFMUJZLGlCQUFpQlksTUFBTSxHQUFHLGtCQUM1Qiw4REFBQ1M7d0JBQUlDLFdBQVU7a0NBQ1p0QixpQkFBaUI0QyxHQUFHLENBQUMsQ0FBQ3pDLHdCQUNyQiw4REFBQ3RDLG1FQUFXQTtnQ0FBa0JzQyxTQUFTQTsrQkFBckJBLFFBQVFxQyxFQUFFOzs7Ozs7Ozs7NkNBSWhDLDhEQUFDbkI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDTDtnQ0FBRUssV0FBVTswQ0FBd0I7Ozs7OzswQ0FDckMsOERBQUNMO2dDQUFFSyxXQUFVOzBDQUE2Qjs7Ozs7OzRCQUN6Q0gsa0NBQ0MsOERBQUNZO2dDQUNDQyxTQUFTZDtnQ0FDVEksV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFULDhEQUFDdUI7Z0JBQU92QixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDTDtzQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1ULDhEQUFDOUMscUVBQVlBO2dCQUNYZ0MsU0FBU25CO2dCQUNUOEQsU0FBUyxJQUFNN0QsbUJBQW1COzs7Ozs7Ozs7Ozs7QUFJMUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy94dXlhbmdiby9naXRodWIuY29tL2xhbmRzY2FwZS9sYW5kc2NhcGUtYXBwL3NyYy9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFByb2plY3QsIENhdGVnb3J5LCBMYW5kc2NhcGVEYXRhLCBGaWx0ZXJTdGF0ZSwgVmlld01vZGUgfSBmcm9tICdAL3R5cGVzL2xhbmRzY2FwZSdcbmltcG9ydCB7IFByb2plY3RDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1Byb2plY3RDYXJkJ1xuaW1wb3J0IHsgU2VhcmNoQmFyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1NlYXJjaEJhcidcbmltcG9ydCB7IENhdGVnb3J5RmlsdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NhdGVnb3J5RmlsdGVyJ1xuaW1wb3J0IHsgTWF0dXJpdHlGaWx0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvTWF0dXJpdHlGaWx0ZXInXG5pbXBvcnQgeyBBcmNoaXRlY3R1cmVEaWFncmFtIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0FyY2hpdGVjdHVyZURpYWdyYW0nXG5pbXBvcnQgeyBWaWV3VG9nZ2xlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1ZpZXdUb2dnbGUnXG5pbXBvcnQgeyBQcm9qZWN0TW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvUHJvamVjdE1vZGFsJ1xuaW1wb3J0IHsgRmlsdGVyLCBYIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCBbZGF0YSwgc2V0RGF0YV0gPSB1c2VTdGF0ZTxMYW5kc2NhcGVEYXRhIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3Nob3dGaWx0ZXJzLCBzZXRTaG93RmlsdGVyc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3ZpZXdNb2RlLCBzZXRWaWV3TW9kZV0gPSB1c2VTdGF0ZTxWaWV3TW9kZT4oeyB0eXBlOiAnYXJjaGl0ZWN0dXJlJyB9KVxuICBjb25zdCBbc2VsZWN0ZWRQcm9qZWN0LCBzZXRTZWxlY3RlZFByb2plY3RdID0gdXNlU3RhdGU8UHJvamVjdCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtmaWx0ZXJzLCBzZXRGaWx0ZXJzXSA9IHVzZVN0YXRlPEZpbHRlclN0YXRlPih7XG4gICAgc2VhcmNoOiAnJyxcbiAgICBjYXRlZ29yeTogJycsXG4gICAgbWF0dXJpdHk6IFtdLFxuICAgIHRhZ3M6IFtdLFxuICAgIGxheWVyOiAnJ1xuICB9KVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvZGF0YS9sYW5kc2NhcGUuanNvbicpXG4gICAgICAgIGNvbnN0IGxhbmRzY2FwZURhdGE6IExhbmRzY2FwZURhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgc2V0RGF0YShsYW5kc2NhcGVEYXRhKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgbGFuZHNjYXBlIGRhdGE6JywgZXJyb3IpXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGxvYWREYXRhKClcbiAgfSwgW10pXG5cbiAgY29uc3QgZmlsdGVyZWRQcm9qZWN0cyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGlmICghZGF0YSkgcmV0dXJuIFtdXG5cbiAgICByZXR1cm4gZGF0YS5wcm9qZWN0cy5maWx0ZXIocHJvamVjdCA9PiB7XG4gICAgICBpZiAoZmlsdGVycy5zZWFyY2gpIHtcbiAgICAgICAgY29uc3Qgc2VhcmNoVGVybSA9IGZpbHRlcnMuc2VhcmNoLnRvTG93ZXJDYXNlKClcbiAgICAgICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IFxuICAgICAgICAgIHByb2plY3QubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8XG4gICAgICAgICAgcHJvamVjdC5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8XG4gICAgICAgICAgcHJvamVjdC50YWdzLnNvbWUodGFnID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKVxuICAgICAgICBcbiAgICAgICAgaWYgKCFtYXRjaGVzU2VhcmNoKSByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgaWYgKGZpbHRlcnMuY2F0ZWdvcnkgJiYgcHJvamVjdC5jYXRlZ29yeSAhPT0gZmlsdGVycy5jYXRlZ29yeSkge1xuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgaWYgKGZpbHRlcnMubWF0dXJpdHkubGVuZ3RoID4gMCAmJiAhZmlsdGVycy5tYXR1cml0eS5pbmNsdWRlcyhwcm9qZWN0Lm1hdHVyaXR5KSkge1xuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9KVxuICB9LCBbZGF0YSwgZmlsdGVyc10pXG5cbiAgY29uc3Qgc3RhdHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIWRhdGEpIHJldHVybiB7IHRvdGFsOiAwLCBjYXRlZ29yaWVzOiAwLCBncmFkdWF0ZWQ6IDAgfVxuXG4gICAgY29uc3QgZ3JhZHVhdGVkID0gZGF0YS5wcm9qZWN0cy5maWx0ZXIocCA9PiBwLm1hdHVyaXR5ID09PSAnZ3JhZHVhdGVkJykubGVuZ3RoXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHRvdGFsOiBkYXRhLnByb2plY3RzLmxlbmd0aCxcbiAgICAgIGNhdGVnb3JpZXM6IGRhdGEuY2F0ZWdvcmllcy5sZW5ndGgsXG4gICAgICBncmFkdWF0ZWRcbiAgICB9XG4gIH0sIFtkYXRhXSlcblxuICBjb25zdCBjbGVhckZpbHRlcnMgPSAoKSA9PiB7XG4gICAgc2V0RmlsdGVycyh7XG4gICAgICBzZWFyY2g6ICcnLFxuICAgICAgY2F0ZWdvcnk6ICcnLFxuICAgICAgbWF0dXJpdHk6IFtdLFxuICAgICAgdGFnczogW10sXG4gICAgICBsYXllcjogJydcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgaGFzQWN0aXZlRmlsdGVycyA9IGZpbHRlcnMuc2VhcmNoIHx8IGZpbHRlcnMuY2F0ZWdvcnkgfHwgZmlsdGVycy5tYXR1cml0eS5sZW5ndGggPiAwIHx8IGZpbHRlcnMubGF5ZXJcblxuICBjb25zdCBoYW5kbGVQcm9qZWN0Q2xpY2sgPSAocHJvamVjdDogUHJvamVjdCkgPT4ge1xuICAgIHNldFNlbGVjdGVkUHJvamVjdChwcm9qZWN0KVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMzIgdy0zMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNjAwXCI+5Yqg6L295LitLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmICghZGF0YSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDBcIj7liqDovb3mlbDmja7lpLHotKU8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICDkupHljp/nlJ/mioDmnK/lhajmma/lm75cbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItOFwiPlxuICAgICAgICAgICAgICDmjqLntKLkupHljp/nlJ/nlJ/mgIHns7vnu5/kuK3nmoTlvIDmupDpobnnm67lkozlt6XlhbdcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00IG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPntzdGF0cy50b3RhbH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTgwMFwiPuaAu+mhueebruaVsDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+e3N0YXRzLmNhdGVnb3JpZXN9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tODAwXCI+5YiG57G75pWwPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXB1cnBsZS01MCBib3JkZXIgYm9yZGVyLXB1cnBsZS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj57c3RhdHMuZ3JhZHVhdGVkfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXB1cnBsZS04MDBcIj7lt7Lmr5XkuJrpobnnm648L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggc3BhY2UteS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWF4LXctbWRcIj5cbiAgICAgICAgICAgICAgPFNlYXJjaEJhclxuICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLnNlYXJjaH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc2VhcmNoOiB2YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLmkJzntKLpobnnm67jgIHmj4/ov7DmiJbmoIfnrb4uLi5cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxWaWV3VG9nZ2xlXG4gICAgICAgICAgICAgICAgdmlld01vZGU9e3ZpZXdNb2RlfVxuICAgICAgICAgICAgICAgIG9uVmlld01vZGVDaGFuZ2U9e3NldFZpZXdNb2RlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0ZpbHRlcnMoIXNob3dGaWx0ZXJzKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEZpbHRlciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICDpq5jnuqfnrZvpgIlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIHtoYXNBY3RpdmVGaWx0ZXJzICYmIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhckZpbHRlcnN9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00IHB5LTIgYmctcmVkLTUwIHRleHQtcmVkLTcwMCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1yZWQtMTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIOa4hemZpOetm+mAiVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7dmlld01vZGUudHlwZSA9PT0gJ2dyaWQnICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8Q2F0ZWdvcnlGaWx0ZXJcbiAgICAgICAgICAgICAgICBjYXRlZ29yaWVzPXtkYXRhLmNhdGVnb3JpZXN9XG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXRlZ29yeT17ZmlsdGVycy5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICBvbkNhdGVnb3J5Q2hhbmdlPXsoY2F0ZWdvcnlJZCkgPT4gc2V0RmlsdGVycyhwcmV2ID0+ICh7IC4uLnByZXYsIGNhdGVnb3J5OiBjYXRlZ29yeUlkIH0pKX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7c2hvd0ZpbHRlcnMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgIDxNYXR1cml0eUZpbHRlclxuICAgICAgICAgICAgICAgIHNlbGVjdGVkTWF0dXJpdHk9e2ZpbHRlcnMubWF0dXJpdHl9XG4gICAgICAgICAgICAgICAgb25NYXR1cml0eUNoYW5nZT17KG1hdHVyaXR5KSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgbWF0dXJpdHkgfSkpfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge3ZpZXdNb2RlLnR5cGUgPT09ICdncmlkJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIOaYvuekuiB7ZmlsdGVyZWRQcm9qZWN0cy5sZW5ndGh9IOS4qumhueebrlxuICAgICAgICAgICAgICB7ZmlsdGVycy5zZWFyY2ggJiYgYCAo5pCc57SiOiBcIiR7ZmlsdGVycy5zZWFyY2h9XCIpYH1cbiAgICAgICAgICAgICAge2ZpbHRlcnMuY2F0ZWdvcnkgJiYgYCAo5YiG57G7OiAke2RhdGEuY2F0ZWdvcmllcy5maW5kKGMgPT4gYy5pZCA9PT0gZmlsdGVycy5jYXRlZ29yeSk/Lm5hbWV9KWB9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge3ZpZXdNb2RlLnR5cGUgPT09ICdhcmNoaXRlY3R1cmUnID8gKFxuICAgICAgICAgIDxBcmNoaXRlY3R1cmVEaWFncmFtXG4gICAgICAgICAgICBsYXllcnM9e2RhdGEuYXJjaGl0ZWN0dXJlTGF5ZXJzIHx8IFtdfVxuICAgICAgICAgICAgb25Qcm9qZWN0Q2xpY2s9e2hhbmRsZVByb2plY3RDbGlja31cbiAgICAgICAgICAgIHNlYXJjaFRlcm09e2ZpbHRlcnMuc2VhcmNofVxuICAgICAgICAgIC8+XG4gICAgICAgICkgOiBmaWx0ZXJlZFByb2plY3RzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIHhsOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICB7ZmlsdGVyZWRQcm9qZWN0cy5tYXAoKHByb2plY3QpID0+IChcbiAgICAgICAgICAgICAgPFByb2plY3RDYXJkIGtleT17cHJvamVjdC5pZH0gcHJvamVjdD17cHJvamVjdH0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1sZ1wiPuayoeacieaJvuWIsOWMuemFjeeahOmhueebrjwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtdC0yXCI+5bCd6K+V6LCD5pW05pCc57Si5p2h5Lu25oiW6L+H5ruk5ZmoPC9wPlxuICAgICAgICAgICAge2hhc0FjdGl2ZUZpbHRlcnMgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Y2xlYXJGaWx0ZXJzfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTQgcHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmuIXpmaTmiYDmnInnrZvpgInmnaHku7ZcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9tYWluPlxuXG4gICAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IG10LTE2XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPHA+JmNvcHk7IDIwMjQg5LqR5Y6f55Sf5oqA5pyv5YWo5pmv5Zu+LiDln7rkuo7lvIDmupDpobnnm67mnoTlu7ouPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuXG4gICAgICB7Lyog6aG555uu6K+m5oOF5qih5oCB5qGGICovfVxuICAgICAgPFByb2plY3RNb2RhbFxuICAgICAgICBwcm9qZWN0PXtzZWxlY3RlZFByb2plY3R9XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNlbGVjdGVkUHJvamVjdChudWxsKX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZU1lbW8iLCJQcm9qZWN0Q2FyZCIsIlNlYXJjaEJhciIsIkNhdGVnb3J5RmlsdGVyIiwiTWF0dXJpdHlGaWx0ZXIiLCJBcmNoaXRlY3R1cmVEaWFncmFtIiwiVmlld1RvZ2dsZSIsIlByb2plY3RNb2RhbCIsIkZpbHRlciIsIlgiLCJIb21lIiwiZGF0YSIsInNldERhdGEiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNob3dGaWx0ZXJzIiwic2V0U2hvd0ZpbHRlcnMiLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwidHlwZSIsInNlbGVjdGVkUHJvamVjdCIsInNldFNlbGVjdGVkUHJvamVjdCIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwic2VhcmNoIiwiY2F0ZWdvcnkiLCJtYXR1cml0eSIsInRhZ3MiLCJsYXllciIsImxvYWREYXRhIiwicmVzcG9uc2UiLCJmZXRjaCIsImxhbmRzY2FwZURhdGEiLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwiZmlsdGVyZWRQcm9qZWN0cyIsInByb2plY3RzIiwiZmlsdGVyIiwicHJvamVjdCIsInNlYXJjaFRlcm0iLCJ0b0xvd2VyQ2FzZSIsIm1hdGNoZXNTZWFyY2giLCJuYW1lIiwiaW5jbHVkZXMiLCJkZXNjcmlwdGlvbiIsInNvbWUiLCJ0YWciLCJsZW5ndGgiLCJzdGF0cyIsInRvdGFsIiwiY2F0ZWdvcmllcyIsImdyYWR1YXRlZCIsInAiLCJjbGVhckZpbHRlcnMiLCJoYXNBY3RpdmVGaWx0ZXJzIiwiaGFuZGxlUHJvamVjdENsaWNrIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwiaDEiLCJtYWluIiwidmFsdWUiLCJvbkNoYW5nZSIsInByZXYiLCJwbGFjZWhvbGRlciIsIm9uVmlld01vZGVDaGFuZ2UiLCJidXR0b24iLCJvbkNsaWNrIiwic2VsZWN0ZWRDYXRlZ29yeSIsIm9uQ2F0ZWdvcnlDaGFuZ2UiLCJjYXRlZ29yeUlkIiwic2VsZWN0ZWRNYXR1cml0eSIsIm9uTWF0dXJpdHlDaGFuZ2UiLCJmaW5kIiwiYyIsImlkIiwibGF5ZXJzIiwiYXJjaGl0ZWN0dXJlTGF5ZXJzIiwib25Qcm9qZWN0Q2xpY2siLCJtYXAiLCJmb290ZXIiLCJvbkNsb3NlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ArchitectureDiagram.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/ArchitectureDiagram.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchitectureDiagram: () => (/* binding */ ArchitectureDiagram)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ProjectBlock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProjectBlock */ \"(ssr)/./src/components/ui/ProjectBlock.tsx\");\n/* __next_internal_client_entry_do_not_use__ ArchitectureDiagram auto */ \n\nfunction ArchitectureDiagram({ layers, onProjectClick, searchTerm }) {\n    const sortedLayers = [\n        ...layers\n    ].sort((a, b)=>a.order - b.order);\n    const highlightProject = (project)=>{\n        if (!searchTerm) return false;\n        const term = searchTerm.toLowerCase();\n        return project.name.toLowerCase().includes(term) || project.description.toLowerCase().includes(term) || project.tags.some((tag)=>tag.toLowerCase().includes(term));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-gray-50 p-6 rounded-lg border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: sortedLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative rounded-lg border-2 p-4\",\n                        style: {\n                            backgroundColor: layer.backgroundColor,\n                            borderColor: layer.borderColor,\n                            minHeight: `${layer.height}px`\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-3 left-4 px-2 py-1 rounded text-sm font-medium\",\n                                style: {\n                                    backgroundColor: layer.color,\n                                    color: 'white'\n                                },\n                                children: layer.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 flex flex-wrap gap-4 min-h-[120px]\",\n                                children: layer.sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded border p-3 flex-1 min-w-[200px]\",\n                                        style: {\n                                            backgroundColor: section.backgroundColor || 'rgba(255, 255, 255, 0.8)',\n                                            borderColor: section.borderColor || '#e5e7eb',\n                                            width: `${section.width * 100}%`\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-3 text-center\",\n                                                children: section.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 justify-center\",\n                                                children: section.projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectBlock__WEBPACK_IMPORTED_MODULE_1__.ProjectBlock, {\n                                                        project: project,\n                                                        onClick: ()=>onProjectClick?.(project),\n                                                        isHighlighted: highlightProject(project),\n                                                        size: project.size || 'medium'\n                                                    }, project.id, false, {\n                                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, section.id, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, layer.id, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-100 border-2 border-blue-300 rounded-lg p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-blue-800 mb-2\",\n                                children: \"服务器\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-6 bg-blue-600 rounded text-xs text-white flex items-center justify-center\",\n                                        children: \"Intel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-6 bg-red-600 rounded text-xs text-white flex items-center justify-center\",\n                                        children: \"AMD\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-6 bg-green-600 rounded text-xs text-white flex items-center justify-center\",\n                                        children: \"ARM\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-100 border-2 border-purple-300 rounded-lg p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-purple-800 mb-2\",\n                                children: \"集中式存储设备\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-purple-600\",\n                                children: \"网络存储 \\xb7 SAN\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-100 border-2 border-yellow-300 rounded-lg p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-yellow-800 mb-2\",\n                                children: \"GPU\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-600 text-white text-xs px-2 py-1 rounded\",\n                                children: \"NVIDIA全系列\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ArchitectureDiagram.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ArchitectureDiagram.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/CategoryFilter.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/CategoryFilter.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryFilter: () => (/* binding */ CategoryFilter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ CategoryFilter auto */ \nfunction CategoryFilter({ categories, selectedCategory, onCategoryChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onCategoryChange(''),\n                className: `px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedCategory === '' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                children: \"全部\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCategoryChange(category.id),\n                    className: `px-4 py-2 rounded-lg text-sm font-medium transition-colors ${selectedCategory === category.id ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    style: {\n                        backgroundColor: selectedCategory === category.id ? category.color : undefined\n                    },\n                    children: category.name\n                }, category.id, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/CategoryFilter.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/CategoryFilter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/MaturityFilter.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/MaturityFilter.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaturityFilter: () => (/* binding */ MaturityFilter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MaturityFilter auto */ \n\nconst maturityLevels = [\n    'graduated',\n    'incubating',\n    'sandbox',\n    'archived'\n];\nfunction MaturityFilter({ selectedMaturity, onMaturityChange }) {\n    const toggleMaturity = (maturity)=>{\n        if (selectedMaturity.includes(maturity)) {\n            onMaturityChange(selectedMaturity.filter((m)=>m !== maturity));\n        } else {\n            onMaturityChange([\n                ...selectedMaturity,\n                maturity\n            ]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"成熟度筛选\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: maturityLevels.map((maturity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: selectedMaturity.includes(maturity),\n                                onChange: ()=>toggleMaturity(maturity),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(maturity)}`,\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(maturity)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, maturity, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/MaturityFilter.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/MaturityFilter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectBlock.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProjectBlock.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectBlock: () => (/* binding */ ProjectBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectBlock auto */ \n\nfunction ProjectBlock({ project, onClick, isHighlighted, size = 'medium' }) {\n    const sizeClasses = {\n        small: 'px-2 py-1 text-xs min-w-[60px] h-8',\n        medium: 'px-3 py-2 text-sm min-w-[80px] h-10',\n        large: 'px-4 py-3 text-base min-w-[120px] h-12'\n    };\n    const getMaturityColor = (maturity)=>{\n        switch(maturity){\n            case 'graduated':\n                return 'bg-green-500 border-green-600 text-white';\n            case 'incubating':\n                return 'bg-blue-500 border-blue-600 text-white';\n            case 'sandbox':\n                return 'bg-yellow-500 border-yellow-600 text-white';\n            case 'archived':\n                return 'bg-gray-500 border-gray-600 text-white';\n            default:\n                return 'bg-gray-400 border-gray-500 text-white';\n        }\n    };\n    const baseClasses = `\n    relative rounded border-2 cursor-pointer transition-all duration-200\n    flex items-center justify-center text-center font-medium\n    hover:shadow-lg hover:scale-105 group\n    ${sizeClasses[size]}\n    ${isHighlighted ? 'ring-2 ring-yellow-400 ring-offset-2' : ''}\n    ${project.color ? '' : getMaturityColor(project.maturity)}\n  `;\n    const customStyle = project.color ? {\n        backgroundColor: project.color,\n        borderColor: project.color,\n        color: 'white'\n    } : {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: baseClasses,\n        style: customStyle,\n        onClick: onClick,\n        title: `${project.name} - ${project.description}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"truncate\",\n                children: project.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1\",\n                children: [\n                    project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: project.homepage_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-3 h-3 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: project.github_url,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-3 h-3 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white\",\n                style: {\n                    backgroundColor: project.maturity === 'graduated' ? '#10b981' : project.maturity === 'incubating' ? '#3b82f6' : project.maturity === 'sandbox' ? '#f59e0b' : '#6b7280'\n                },\n                title: `成熟度: ${project.maturity}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectBlock.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectBlock.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ProjectCard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectCard: () => (/* binding */ ProjectCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectCard auto */ \n\n\nfunction ProjectCard({ project }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: project.name.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: project.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(project.maturity)}`,\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(project.maturity)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.homepage_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.github_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm mb-4 line-clamp-3\",\n                children: project.description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 mb-4\",\n                children: [\n                    project.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                            children: tag\n                        }, tag, false, {\n                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)),\n                    project.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\",\n                        children: [\n                            \"+\",\n                            project.tags.length - 3\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            project.stars && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.stars)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            project.contributors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.contributors)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    project.language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                        children: project.language\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectCard.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ProjectModal.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProjectModal.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectModal: () => (/* binding */ ProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Star,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectModal auto */ \n\n\nfunction ProjectModal({ project, onClose }) {\n    if (!project) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl font-bold text-gray-600\",\n                                            children: project.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: project.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityColor)(project.maturity)}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMaturityLabel)(project.maturity)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"项目描述\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: project.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            project.stars && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-yellow-600 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.stars)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Stars\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            project.contributors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-blue-600 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatNumber)(project.contributors)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"贡献者\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this),\n                            project.license && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-gray-900 mb-1\",\n                                        children: project.license\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"许可证\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            project.language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-gray-900 mb-1\",\n                                        children: project.language\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"主要语言\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    project.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                children: \"标签\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: project.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                        children: tag\n                                    }, tag, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            project.homepage_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.homepage_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"访问官网\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            project.github_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: project.github_url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Star_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"查看源码\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ProjectModal.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ProjectModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/SearchBar.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/SearchBar.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ SearchBar auto */ \n\nfunction SearchBar({ value, onChange, placeholder = \"搜索项目...\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: placeholder\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/SearchBar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ViewToggle.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/ViewToggle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewToggle: () => (/* binding */ ViewToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Network!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Network!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* __next_internal_client_entry_do_not_use__ ViewToggle auto */ \n\nfunction ViewToggle({ viewMode, onViewModeChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex bg-gray-100 rounded-lg p-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onViewModeChange({\n                        type: 'grid'\n                    }),\n                className: `flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${viewMode.type === 'grid' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"网格视图\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onViewModeChange({\n                        type: 'architecture'\n                    }),\n                className: `flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${viewMode.type === 'architecture' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Network_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"架构图\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/github.com/landscape/landscape-app/src/components/ui/ViewToggle.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ViewToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getMaturityColor: () => (/* binding */ getMaturityColor),\n/* harmony export */   getMaturityLabel: () => (/* binding */ getMaturityLabel)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\nfunction getMaturityColor(maturity) {\n    switch(maturity){\n        case 'graduated':\n            return 'bg-green-100 text-green-800 border-green-200';\n        case 'incubating':\n            return 'bg-blue-100 text-blue-800 border-blue-200';\n        case 'sandbox':\n            return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        case 'archived':\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n        default:\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n}\nfunction getMaturityLabel(maturity) {\n    switch(maturity){\n        case 'graduated':\n            return '已毕业';\n        case 'incubating':\n            return '孵化中';\n        case 'sandbox':\n            return '沙盒';\n        case 'archived':\n            return '已归档';\n        default:\n            return maturity;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fxuyangbo%2Fgithub.com%2Flandscape%2Flandscape-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();