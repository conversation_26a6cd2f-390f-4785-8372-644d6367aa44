{"version": 3, "sources": ["../../../src/shared/lib/amp-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n"], "names": ["AmpStateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": ";;;;+BAEaA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,kBAAsCC,cAAK,CAACC,aAAa,CAAC,CAAC;AAExE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCL,gBAAgBM,WAAW,GAAG;AAChC"}