{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-pathname-normalizer.ts"], "sourcesContent": ["import { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport { Normalizers } from '../../normalizers'\nimport { wrapNormalizerFn } from '../../wrap-normalizer-fn'\nimport { UnderscoreNormalizer } from '../../underscore-normalizer'\nimport type { Normalizer } from '../../normalizer'\n\nexport class App<PERSON>athnameNormalizer extends Normalizers {\n  constructor() {\n    super([\n      // The pathname to match should have the trailing `/page` and other route\n      // group information stripped from it.\n      wrapNormalizerFn(normalizeAppPath),\n      // The page should have the `%5F` characters replaced with `_` characters.\n      new UnderscoreNormalizer(),\n    ])\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(page)\n  }\n}\n\nexport class DevAppPathnameNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppPathnameNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["normalizeAppPath", "Normalizers", "wrapNormalizerFn", "UnderscoreNormalizer", "AppPathnameNormalizer", "constructor", "normalize", "page", "DevAppPathnameNormalizer", "pageNormalizer", "filename"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,oBAAoB,QAAQ,8BAA6B;AAGlE,OAAO,MAAMC,8BAA8BH;IACzCI,aAAc;QACZ,KAAK,CAAC;YACJ,yEAAyE;YACzE,sCAAsC;YACtCH,iBAAiBF;YACjB,0EAA0E;YAC1E,IAAIG;SACL;IACH;IAEOG,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF;AAEA,OAAO,MAAMC,iCAAiCP;IAC5CI,YAAYI,cAA0B,CAAE;QACtC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIL;SACL;IACH;IAEOE,UAAUI,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACJ,UAAUI;IACzB;AACF"}