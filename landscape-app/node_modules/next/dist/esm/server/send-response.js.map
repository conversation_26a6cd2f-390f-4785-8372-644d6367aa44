{"version": 3, "sources": ["../../src/server/send-response.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport { isNodeNextResponse } from './base-http/helpers'\n\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { splitCookiesString } from './web/utils'\n\n/**\n * Sends the response on the underlying next response object.\n *\n * @param req the underlying request object\n * @param res the underlying response object\n * @param response the response to send\n */\nexport async function sendResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  response: Response,\n  waitUntil?: Promise<unknown>\n): Promise<void> {\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextResponse(res)\n  ) {\n    // Copy over the response status.\n    res.statusCode = response.status\n    res.statusMessage = response.statusText\n\n    // TODO: this is not spec-compliant behavior and we should not restrict\n    // headers that are allowed to appear many times.\n    //\n    // See:\n    // https://github.com/vercel/next.js/pull/70127\n    const headersWithMultipleValuesAllowed = [\n      // can add more headers to this list if needed\n      'set-cookie',\n      'www-authenticate',\n      'proxy-authenticate',\n      'vary',\n    ]\n\n    // Copy over the response headers.\n    response.headers?.forEach((value, name) => {\n      // `x-middleware-set-cookie` is an internal header not needed for the response\n      if (name.toLowerCase() === 'x-middleware-set-cookie') {\n        return\n      }\n\n      // The append handling is special cased for `set-cookie`.\n      if (name.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          res.appendHeader(name, cookie)\n        }\n      } else {\n        // only append the header if it is either not present in the outbound response\n        // or if the header supports multiple values\n        const isHeaderPresent = typeof res.getHeader(name) !== 'undefined'\n        if (\n          headersWithMultipleValuesAllowed.includes(name.toLowerCase()) ||\n          !isHeaderPresent\n        ) {\n          res.appendHeader(name, value)\n        }\n      }\n    })\n\n    /**\n     * The response can't be directly piped to the underlying response. The\n     * following is duplicated from the edge runtime handler.\n     *\n     * See packages/next/server/next-server.ts\n     */\n\n    const { originalResponse } = res\n\n    // A response body must not be sent for HEAD requests. See https://httpwg.org/specs/rfc9110.html#HEAD\n    if (response.body && req.method !== 'HEAD') {\n      await pipeToNodeResponse(response.body, originalResponse, waitUntil)\n    } else {\n      originalResponse.end()\n    }\n  }\n}\n"], "names": ["isNodeNextResponse", "pipeToNodeResponse", "splitCookiesString", "sendResponse", "req", "res", "response", "waitUntil", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headersWithMultipleValuesAllowed", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "append<PERSON><PERSON>er", "isHeaderPresent", "<PERSON><PERSON><PERSON><PERSON>", "includes", "originalResponse", "body", "method", "end"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,sBAAqB;AAExD,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,kBAAkB,QAAQ,cAAa;AAEhD;;;;;;CAMC,GACD,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB,EAClBC,SAA4B;IAE5B,IACE,qEAAqE;IACrE,6DAA6D;IAC7DC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BV,mBAAmBK,MACnB;YAkBA,kCAAkC;QAClCC;QAlBA,iCAAiC;QACjCD,IAAIM,UAAU,GAAGL,SAASM,MAAM;QAChCP,IAAIQ,aAAa,GAAGP,SAASQ,UAAU;QAEvC,uEAAuE;QACvE,iDAAiD;QACjD,EAAE;QACF,OAAO;QACP,+CAA+C;QAC/C,MAAMC,mCAAmC;YACvC,8CAA8C;YAC9C;YACA;YACA;YACA;SACD;SAGDT,oBAAAA,SAASU,OAAO,qBAAhBV,kBAAkBW,OAAO,CAAC,CAACC,OAAOC;YAChC,8EAA8E;YAC9E,IAAIA,KAAKC,WAAW,OAAO,2BAA2B;gBACpD;YACF;YAEA,yDAAyD;YACzD,IAAID,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUnB,mBAAmBgB,OAAQ;oBAC9Cb,IAAIiB,YAAY,CAACH,MAAME;gBACzB;YACF,OAAO;gBACL,8EAA8E;gBAC9E,4CAA4C;gBAC5C,MAAME,kBAAkB,OAAOlB,IAAImB,SAAS,CAACL,UAAU;gBACvD,IACEJ,iCAAiCU,QAAQ,CAACN,KAAKC,WAAW,OAC1D,CAACG,iBACD;oBACAlB,IAAIiB,YAAY,CAACH,MAAMD;gBACzB;YACF;QACF;QAEA;;;;;KAKC,GAED,MAAM,EAAEQ,gBAAgB,EAAE,GAAGrB;QAE7B,qGAAqG;QACrG,IAAIC,SAASqB,IAAI,IAAIvB,IAAIwB,MAAM,KAAK,QAAQ;YAC1C,MAAM3B,mBAAmBK,SAASqB,IAAI,EAAED,kBAAkBnB;QAC5D,OAAO;YACLmB,iBAAiBG,GAAG;QACtB;IACF;AACF"}