{"version": 3, "sources": ["../../../src/server/route-matcher-providers/app-page-route-matcher-provider.ts"], "sourcesContent": ["import { isAppPageRoute } from '../../lib/is-app-page-route'\n\nimport { APP_PATHS_MANIFEST } from '../../shared/lib/constants'\nimport { AppNormalizers } from '../normalizers/built/app'\nimport { RouteKind } from '../route-kind'\nimport { AppPageRouteMatcher } from '../route-matchers/app-page-route-matcher'\nimport type {\n  Manifest,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\n\nexport class AppPageRouteMatcherProvider extends ManifestRouteMatcherProvider<AppPageRouteMatcher> {\n  private readonly normalizers: AppNormalizers\n\n  constructor(distDir: string, manifestLoader: ManifestLoader) {\n    super(APP_PATHS_MANIFEST, manifestLoader)\n\n    this.normalizers = new AppNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<AppPageRouteMatcher>> {\n    // This matcher only matches app pages.\n    const pages = Object.keys(manifest).filter((page) => isAppPageRoute(page))\n\n    // Collect all the app paths for each page. This could include any parallel\n    // routes.\n    const allAppPaths: Record<string, string[]> = {}\n    for (const page of pages) {\n      const pathname = this.normalizers.pathname.normalize(page)\n      if (pathname in allAppPaths) allAppPaths[pathname].push(page)\n      else allAppPaths[pathname] = [page]\n    }\n\n    // Format the routes.\n    const matchers: Array<AppPageRouteMatcher> = []\n    for (const [pathname, appPaths] of Object.entries(allAppPaths)) {\n      // TODO-APP: (wyattjoh) this is a hack right now, should be more deterministic\n      const page = appPaths[0]\n\n      const filename = this.normalizers.filename.normalize(manifest[page])\n      const bundlePath = this.normalizers.bundlePath.normalize(page)\n\n      matchers.push(\n        new AppPageRouteMatcher({\n          kind: RouteKind.APP_PAGE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n          appPaths,\n        })\n      )\n    }\n\n    return matchers\n  }\n}\n"], "names": ["isAppPageRoute", "APP_PATHS_MANIFEST", "AppNormalizers", "RouteKind", "AppPageRouteMatcher", "ManifestRouteMatcherProvider", "AppPageRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "normalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "allAppPaths", "pathname", "normalize", "push", "matchers", "appPaths", "entries", "filename", "bundlePath", "kind", "APP_PAGE"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA6B;AAE5D,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,SAAS,QAAQ,gBAAe;AACzC,SAASC,mBAAmB,QAAQ,2CAA0C;AAK9E,SAASC,4BAA4B,QAAQ,oCAAmC;AAEhF,OAAO,MAAMC,oCAAoCD;IAG/CE,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACR,oBAAoBQ;QAE1B,IAAI,CAACC,WAAW,GAAG,IAAIR,eAAeM;IACxC;IAEA,MAAgBG,UACdC,QAAkB,EAC2B;QAC7C,uCAAuC;QACvC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASjB,eAAeiB;QAEpE,2EAA2E;QAC3E,UAAU;QACV,MAAMC,cAAwC,CAAC;QAC/C,KAAK,MAAMD,QAAQJ,MAAO;YACxB,MAAMM,WAAW,IAAI,CAACT,WAAW,CAACS,QAAQ,CAACC,SAAS,CAACH;YACrD,IAAIE,YAAYD,aAAaA,WAAW,CAACC,SAAS,CAACE,IAAI,CAACJ;iBACnDC,WAAW,CAACC,SAAS,GAAG;gBAACF;aAAK;QACrC;QAEA,qBAAqB;QACrB,MAAMK,WAAuC,EAAE;QAC/C,KAAK,MAAM,CAACH,UAAUI,SAAS,IAAIT,OAAOU,OAAO,CAACN,aAAc;YAC9D,8EAA8E;YAC9E,MAAMD,OAAOM,QAAQ,CAAC,EAAE;YAExB,MAAME,WAAW,IAAI,CAACf,WAAW,CAACe,QAAQ,CAACL,SAAS,CAACR,QAAQ,CAACK,KAAK;YACnE,MAAMS,aAAa,IAAI,CAAChB,WAAW,CAACgB,UAAU,CAACN,SAAS,CAACH;YAEzDK,SAASD,IAAI,CACX,IAAIjB,oBAAoB;gBACtBuB,MAAMxB,UAAUyB,QAAQ;gBACxBT;gBACAF;gBACAS;gBACAD;gBACAF;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}