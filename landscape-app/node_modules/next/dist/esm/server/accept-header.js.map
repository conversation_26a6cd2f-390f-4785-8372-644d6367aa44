{"version": 3, "sources": ["../../src/server/accept-header.ts"], "sourcesContent": ["interface Selection {\n  pos: number\n  pref?: number\n  q: number\n  token: string\n}\n\ninterface Options {\n  prefixMatch?: boolean\n  type: 'accept-language'\n}\n\nfunction parse(\n  raw: string,\n  preferences: readonly string[] | undefined,\n  options: Options\n) {\n  const lowers = new Map<string, { orig: string; pos: number }>()\n  const header = raw.replace(/[ \\t]/g, '')\n\n  if (preferences) {\n    let pos = 0\n    for (const preference of preferences) {\n      const lower = preference.toLowerCase()\n      lowers.set(lower, { orig: preference, pos: pos++ })\n      if (options.prefixMatch) {\n        const parts = lower.split('-')\n        while ((parts.pop(), parts.length > 0)) {\n          const joined = parts.join('-')\n          if (!lowers.has(joined)) {\n            lowers.set(joined, { orig: preference, pos: pos++ })\n          }\n        }\n      }\n    }\n  }\n\n  const parts = header.split(',')\n  const selections: Selection[] = []\n  const map = new Set<string>()\n\n  for (let i = 0; i < parts.length; ++i) {\n    const part = parts[i]\n    if (!part) {\n      continue\n    }\n\n    const params = part.split(';')\n    if (params.length > 2) {\n      throw new Error(`Invalid ${options.type} header`)\n    }\n\n    let token = params[0].toLowerCase()\n    if (!token) {\n      throw new Error(`Invalid ${options.type} header`)\n    }\n\n    const selection: Selection = { token, pos: i, q: 1 }\n    if (preferences && lowers.has(token)) {\n      selection.pref = lowers.get(token)!.pos\n    }\n\n    map.add(selection.token)\n\n    if (params.length === 2) {\n      const q = params[1]\n      const [key, value] = q.split('=')\n\n      if (!value || (key !== 'q' && key !== 'Q')) {\n        throw new Error(`Invalid ${options.type} header`)\n      }\n\n      const score = parseFloat(value)\n      if (score === 0) {\n        continue\n      }\n\n      if (Number.isFinite(score) && score <= 1 && score >= 0.001) {\n        selection.q = score\n      }\n    }\n\n    selections.push(selection)\n  }\n\n  selections.sort((a, b) => {\n    if (b.q !== a.q) {\n      return b.q - a.q\n    }\n\n    if (b.pref !== a.pref) {\n      if (a.pref === undefined) {\n        return 1\n      }\n\n      if (b.pref === undefined) {\n        return -1\n      }\n\n      return a.pref - b.pref\n    }\n\n    return a.pos - b.pos\n  })\n\n  const values = selections.map((selection) => selection.token)\n  if (!preferences || !preferences.length) {\n    return values\n  }\n\n  const preferred: string[] = []\n  for (const selection of values) {\n    if (selection === '*') {\n      for (const [preference, value] of lowers) {\n        if (!map.has(preference)) {\n          preferred.push(value.orig)\n        }\n      }\n    } else {\n      const lower = selection.toLowerCase()\n      if (lowers.has(lower)) {\n        preferred.push(lowers.get(lower)!.orig)\n      }\n    }\n  }\n\n  return preferred\n}\n\nexport function acceptLanguage(header = '', preferences?: readonly string[]) {\n  return (\n    parse(header, preferences, {\n      type: 'accept-language',\n      prefixMatch: true,\n    })[0] || ''\n  )\n}\n"], "names": ["parse", "raw", "preferences", "options", "lowers", "Map", "header", "replace", "pos", "preference", "lower", "toLowerCase", "set", "orig", "prefixMatch", "parts", "split", "pop", "length", "joined", "join", "has", "selections", "map", "Set", "i", "part", "params", "Error", "type", "token", "selection", "q", "pref", "get", "add", "key", "value", "score", "parseFloat", "Number", "isFinite", "push", "sort", "a", "b", "undefined", "values", "preferred", "acceptLanguage"], "mappings": "AAYA,SAASA,MACPC,GAAW,EACXC,WAA0C,EAC1CC,OAAgB;IAEhB,MAAMC,SAAS,IAAIC;IACnB,MAAMC,SAASL,IAAIM,OAAO,CAAC,UAAU;IAErC,IAAIL,aAAa;QACf,IAAIM,MAAM;QACV,KAAK,MAAMC,cAAcP,YAAa;YACpC,MAAMQ,QAAQD,WAAWE,WAAW;YACpCP,OAAOQ,GAAG,CAACF,OAAO;gBAAEG,MAAMJ;gBAAYD,KAAKA;YAAM;YACjD,IAAIL,QAAQW,WAAW,EAAE;gBACvB,MAAMC,QAAQL,MAAMM,KAAK,CAAC;gBAC1B,MAAQD,MAAME,GAAG,IAAIF,MAAMG,MAAM,GAAG,EAAI;oBACtC,MAAMC,SAASJ,MAAMK,IAAI,CAAC;oBAC1B,IAAI,CAAChB,OAAOiB,GAAG,CAACF,SAAS;wBACvBf,OAAOQ,GAAG,CAACO,QAAQ;4BAAEN,MAAMJ;4BAAYD,KAAKA;wBAAM;oBACpD;gBACF;YACF;QACF;IACF;IAEA,MAAMO,QAAQT,OAAOU,KAAK,CAAC;IAC3B,MAAMM,aAA0B,EAAE;IAClC,MAAMC,MAAM,IAAIC;IAEhB,IAAK,IAAIC,IAAI,GAAGA,IAAIV,MAAMG,MAAM,EAAE,EAAEO,EAAG;QACrC,MAAMC,OAAOX,KAAK,CAACU,EAAE;QACrB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,MAAMC,SAASD,KAAKV,KAAK,CAAC;QAC1B,IAAIW,OAAOT,MAAM,GAAG,GAAG;YACrB,MAAM,qBAA2C,CAA3C,IAAIU,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC,GAA1C,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,IAAIC,QAAQH,MAAM,CAAC,EAAE,CAAChB,WAAW;QACjC,IAAI,CAACmB,OAAO;YACV,MAAM,qBAA2C,CAA3C,IAAIF,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC,GAA1C,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,MAAME,YAAuB;YAAED;YAAOtB,KAAKiB;YAAGO,GAAG;QAAE;QACnD,IAAI9B,eAAeE,OAAOiB,GAAG,CAACS,QAAQ;YACpCC,UAAUE,IAAI,GAAG7B,OAAO8B,GAAG,CAACJ,OAAQtB,GAAG;QACzC;QAEAe,IAAIY,GAAG,CAACJ,UAAUD,KAAK;QAEvB,IAAIH,OAAOT,MAAM,KAAK,GAAG;YACvB,MAAMc,IAAIL,MAAM,CAAC,EAAE;YACnB,MAAM,CAACS,KAAKC,MAAM,GAAGL,EAAEhB,KAAK,CAAC;YAE7B,IAAI,CAACqB,SAAUD,QAAQ,OAAOA,QAAQ,KAAM;gBAC1C,MAAM,qBAA2C,CAA3C,IAAIR,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC,GAA1C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0C;YAClD;YAEA,MAAMS,QAAQC,WAAWF;YACzB,IAAIC,UAAU,GAAG;gBACf;YACF;YAEA,IAAIE,OAAOC,QAAQ,CAACH,UAAUA,SAAS,KAAKA,SAAS,OAAO;gBAC1DP,UAAUC,CAAC,GAAGM;YAChB;QACF;QAEAhB,WAAWoB,IAAI,CAACX;IAClB;IAEAT,WAAWqB,IAAI,CAAC,CAACC,GAAGC;QAClB,IAAIA,EAAEb,CAAC,KAAKY,EAAEZ,CAAC,EAAE;YACf,OAAOa,EAAEb,CAAC,GAAGY,EAAEZ,CAAC;QAClB;QAEA,IAAIa,EAAEZ,IAAI,KAAKW,EAAEX,IAAI,EAAE;YACrB,IAAIW,EAAEX,IAAI,KAAKa,WAAW;gBACxB,OAAO;YACT;YAEA,IAAID,EAAEZ,IAAI,KAAKa,WAAW;gBACxB,OAAO,CAAC;YACV;YAEA,OAAOF,EAAEX,IAAI,GAAGY,EAAEZ,IAAI;QACxB;QAEA,OAAOW,EAAEpC,GAAG,GAAGqC,EAAErC,GAAG;IACtB;IAEA,MAAMuC,SAASzB,WAAWC,GAAG,CAAC,CAACQ,YAAcA,UAAUD,KAAK;IAC5D,IAAI,CAAC5B,eAAe,CAACA,YAAYgB,MAAM,EAAE;QACvC,OAAO6B;IACT;IAEA,MAAMC,YAAsB,EAAE;IAC9B,KAAK,MAAMjB,aAAagB,OAAQ;QAC9B,IAAIhB,cAAc,KAAK;YACrB,KAAK,MAAM,CAACtB,YAAY4B,MAAM,IAAIjC,OAAQ;gBACxC,IAAI,CAACmB,IAAIF,GAAG,CAACZ,aAAa;oBACxBuC,UAAUN,IAAI,CAACL,MAAMxB,IAAI;gBAC3B;YACF;QACF,OAAO;YACL,MAAMH,QAAQqB,UAAUpB,WAAW;YACnC,IAAIP,OAAOiB,GAAG,CAACX,QAAQ;gBACrBsC,UAAUN,IAAI,CAACtC,OAAO8B,GAAG,CAACxB,OAAQG,IAAI;YACxC;QACF;IACF;IAEA,OAAOmC;AACT;AAEA,OAAO,SAASC,eAAe3C,SAAS,EAAE,EAAEJ,WAA+B;IACzE,OACEF,MAAMM,QAAQJ,aAAa;QACzB2B,MAAM;QACNf,aAAa;IACf,EAAE,CAAC,EAAE,IAAI;AAEb"}