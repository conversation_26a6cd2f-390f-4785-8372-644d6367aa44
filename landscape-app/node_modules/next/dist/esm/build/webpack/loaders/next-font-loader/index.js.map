{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/index.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>oa<PERSON> } from '../../../../../font'\n\nimport path from 'path'\nimport { bold, cyan } from '../../../../lib/picocolors'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport postcssNextFontPlugin from './postcss-next-font'\nimport { promisify } from 'util'\n\nexport default async function nextFontLoader(this: any) {\n  const nextFontLoaderSpan =\n    this.currentTraceSpan.traceChild('next-font-loader')\n  return nextFontLoaderSpan.traceAsyncFn(async () => {\n    const callback = this.async()\n\n    /**\n     * The next-swc plugin next-transform-font turns font function calls into CSS imports.\n     * At the end of the import, it adds the call arguments and some additional data as a resourceQuery.\n     * e.g:\n     * const inter = Inter({ subset: ['latin'] })\n     * ->\n     * import inter from 'next/font/google/target.css?{\"import\":\"Inter\",\"subsets\":[\"latin\"]}'\n     *\n     * Here we parse the resourceQuery to get the font function name, call arguments, and the path to the file that called the font function.\n     */\n    const {\n      path: relativeFilePathFromRoot,\n      import: functionName,\n      arguments: data,\n      variableName,\n    } = JSON.parse(this.resourceQuery.slice(1))\n\n    // Throw error if @next/font is used in _document.js\n    if (/pages[\\\\/]_document\\./.test(relativeFilePathFromRoot)) {\n      const err = new Error(\n        `${bold('Cannot')} be used within ${cyan('pages/_document.js')}.`\n      )\n      err.name = 'NextFontError'\n      callback(err)\n      return\n    }\n\n    const {\n      isDev,\n      isServer,\n      assetPrefix,\n      fontLoaderPath,\n      postcss: getPostcss,\n    } = this.getOptions()\n\n    if (assetPrefix && !/^\\/|https?:\\/\\//.test(assetPrefix)) {\n      const err = new Error(\n        'assetPrefix must start with a leading slash or be an absolute URL(http:// or https://)'\n      )\n      err.name = 'NextFontError'\n      callback(err)\n      return\n    }\n\n    /**\n     * Emit font files to .next/static/media as [hash].[ext].\n     *\n     * If the font should be preloaded, add .p to the filename: [hash].p.[ext]\n     * NextFontManifestPlugin adds these files to the next/font manifest.\n     *\n     * If the font is using a size-adjust fallback font, add -s to the filename: [hash]-s.[ext]\n     * NextFontManifestPlugin uses this to see if fallback fonts are being used.\n     * This is used to collect stats on fallback fonts usage by the Google Aurora team.\n     */\n    const emitFontFile = (\n      content: Buffer,\n      ext: string,\n      preload: boolean,\n      isUsingSizeAdjust?: boolean\n    ) => {\n      const opts = { context: this.rootContext, content }\n      const interpolatedName = loaderUtils.interpolateName(\n        this,\n        `static/media/[hash]${isUsingSizeAdjust ? '-s' : ''}${\n          preload ? '.p' : ''\n        }.${ext}`,\n        opts\n      )\n      const outputPath = `${assetPrefix}/_next/${interpolatedName}`\n      // Only the client emits the font file\n      if (!isServer) {\n        this.emitFile(interpolatedName, content, null)\n      }\n      // But both the server and client must get the resulting path\n      return outputPath\n    }\n\n    try {\n      // Import the font loader function from either next/font/local or next/font/google\n      // The font loader function emits font files and returns @font-faces and fallback font metrics\n      const fontLoader: FontLoader = require(fontLoaderPath).default\n      let { css, fallbackFonts, adjustFontFallback, weight, style, variable } =\n        await nextFontLoaderSpan.traceChild('font-loader').traceAsyncFn(() =>\n          fontLoader({\n            functionName,\n            variableName,\n            data,\n            emitFontFile,\n            resolve: (src: string) =>\n              promisify(this.resolve)(\n                path.dirname(\n                  path.join(this.rootContext, relativeFilePathFromRoot)\n                ),\n                src.startsWith('.') ? src : `./${src}`\n              ),\n            isDev,\n            isServer,\n            loaderContext: this,\n          })\n        )\n\n      const { postcss } = await getPostcss()\n\n      // Exports will be exported as is from css-loader instead of a CSS module export\n      const exports: { name: any; value: any }[] = []\n\n      // Generate a hash from the CSS content. Used to generate classnames\n      const fontFamilyHash = loaderUtils.getHashDigest(\n        Buffer.from(css),\n        'sha1',\n        'hex',\n        6\n      )\n\n      // Add CSS classes, exports and make the font-family locally scoped by turning it unguessable\n      const result = await nextFontLoaderSpan\n        .traceChild('postcss')\n        .traceAsyncFn(() =>\n          postcss(\n            postcssNextFontPlugin({\n              exports,\n              fallbackFonts,\n              weight,\n              style,\n              adjustFontFallback,\n              variable,\n            })\n          ).process(css, {\n            from: undefined,\n          })\n        )\n\n      const ast = {\n        type: 'postcss',\n        version: result.processor.version,\n        root: result.root,\n      }\n\n      // Return the resulting CSS and send the postcss ast, font exports and the hash to the css-loader in the meta argument.\n      callback(null, result.css, null, {\n        exports,\n        ast,\n        fontFamilyHash,\n      })\n    } catch (err: any) {\n      callback(err)\n    }\n  })\n}\n"], "names": ["path", "bold", "cyan", "loaderUtils", "postcssNextFontPlugin", "promisify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFontLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "callback", "async", "relativeFilePathFromRoot", "import", "functionName", "arguments", "data", "variableName", "JSON", "parse", "resourceQuery", "slice", "test", "err", "Error", "name", "isDev", "isServer", "assetPrefix", "fontLoaderPath", "postcss", "getPostcss", "getOptions", "emitFontFile", "content", "ext", "preload", "isUsingSizeAdjust", "opts", "context", "rootContext", "interpolatedName", "interpolateName", "outputPath", "emitFile", "fontLoader", "require", "default", "css", "fallbackFonts", "adjustFontFallback", "weight", "style", "variable", "resolve", "src", "dirname", "join", "startsWith", "loaderContext", "exports", "fontFamilyHash", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "result", "process", "undefined", "ast", "type", "version", "processor", "root"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,SAASC,IAAI,EAAEC,IAAI,QAAQ,6BAA4B;AACvD,OAAOC,iBAAiB,mCAAkC;AAC1D,OAAOC,2BAA2B,sBAAqB;AACvD,SAASC,SAAS,QAAQ,OAAM;AAEhC,eAAe,eAAeC;IAC5B,MAAMC,qBACJ,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACnC,OAAOF,mBAAmBG,YAAY,CAAC;QACrC,MAAMC,WAAW,IAAI,CAACC,KAAK;QAE3B;;;;;;;;;KASC,GACD,MAAM,EACJZ,MAAMa,wBAAwB,EAC9BC,QAAQC,YAAY,EACpBC,WAAWC,IAAI,EACfC,YAAY,EACb,GAAGC,KAAKC,KAAK,CAAC,IAAI,CAACC,aAAa,CAACC,KAAK,CAAC;QAExC,oDAAoD;QACpD,IAAI,wBAAwBC,IAAI,CAACV,2BAA2B;YAC1D,MAAMW,MAAM,qBAEX,CAFW,IAAIC,MACd,GAAGxB,KAAK,UAAU,gBAAgB,EAAEC,KAAK,sBAAsB,CAAC,CAAC,GADvD,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAsB,IAAIE,IAAI,GAAG;YACXf,SAASa;YACT;QACF;QAEA,MAAM,EACJG,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,cAAc,EACdC,SAASC,UAAU,EACpB,GAAG,IAAI,CAACC,UAAU;QAEnB,IAAIJ,eAAe,CAAC,kBAAkBN,IAAI,CAACM,cAAc;YACvD,MAAML,MAAM,qBAEX,CAFW,IAAIC,MACd,2FADU,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAD,IAAIE,IAAI,GAAG;YACXf,SAASa;YACT;QACF;QAEA;;;;;;;;;KASC,GACD,MAAMU,eAAe,CACnBC,SACAC,KACAC,SACAC;YAEA,MAAMC,OAAO;gBAAEC,SAAS,IAAI,CAACC,WAAW;gBAAEN;YAAQ;YAClD,MAAMO,mBAAmBvC,YAAYwC,eAAe,CAClD,IAAI,EACJ,CAAC,mBAAmB,EAAEL,oBAAoB,OAAO,KAC/CD,UAAU,OAAO,GAClB,CAAC,EAAED,KAAK,EACTG;YAEF,MAAMK,aAAa,GAAGf,YAAY,OAAO,EAAEa,kBAAkB;YAC7D,sCAAsC;YACtC,IAAI,CAACd,UAAU;gBACb,IAAI,CAACiB,QAAQ,CAACH,kBAAkBP,SAAS;YAC3C;YACA,6DAA6D;YAC7D,OAAOS;QACT;QAEA,IAAI;YACF,kFAAkF;YAClF,8FAA8F;YAC9F,MAAME,aAAyBC,QAAQjB,gBAAgBkB,OAAO;YAC9D,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GACrE,MAAM/C,mBAAmBE,UAAU,CAAC,eAAeC,YAAY,CAAC,IAC9DoC,WAAW;oBACT/B;oBACAG;oBACAD;oBACAiB;oBACAqB,SAAS,CAACC,MACRnD,UAAU,IAAI,CAACkD,OAAO,EACpBvD,KAAKyD,OAAO,CACVzD,KAAK0D,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE5B,4BAE9B2C,IAAIG,UAAU,CAAC,OAAOH,MAAM,CAAC,EAAE,EAAEA,KAAK;oBAE1C7B;oBACAC;oBACAgC,eAAe,IAAI;gBACrB;YAGJ,MAAM,EAAE7B,OAAO,EAAE,GAAG,MAAMC;YAE1B,gFAAgF;YAChF,MAAM6B,UAAuC,EAAE;YAE/C,oEAAoE;YACpE,MAAMC,iBAAiB3D,YAAY4D,aAAa,CAC9CC,OAAOC,IAAI,CAAChB,MACZ,QACA,OACA;YAGF,6FAA6F;YAC7F,MAAMiB,SAAS,MAAM3D,mBAClBE,UAAU,CAAC,WACXC,YAAY,CAAC,IACZqB,QACE3B,sBAAsB;oBACpByD;oBACAX;oBACAE;oBACAC;oBACAF;oBACAG;gBACF,IACAa,OAAO,CAAClB,KAAK;oBACbgB,MAAMG;gBACR;YAGJ,MAAMC,MAAM;gBACVC,MAAM;gBACNC,SAASL,OAAOM,SAAS,CAACD,OAAO;gBACjCE,MAAMP,OAAOO,IAAI;YACnB;YAEA,uHAAuH;YACvH9D,SAAS,MAAMuD,OAAOjB,GAAG,EAAE,MAAM;gBAC/BY;gBACAQ;gBACAP;YACF;QACF,EAAE,OAAOtC,KAAU;YACjBb,SAASa;QACX;IACF;AACF"}