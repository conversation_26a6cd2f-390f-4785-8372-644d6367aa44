{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport type { Span } from '../../../trace'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { getCompilationSpan } from '../utils'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n    absolutePathByEntryName: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private compilerType: CompilerNameValues\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.compilerType = compilerType\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(compilation: webpack.Compilation, span: Span) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of entrypoint\n          .getEntrypointChunk()\n          .getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/') && this.appDir) {\n          const appDirRelativeEntryPath =\n            this.buildTraceContext.entriesTrace?.absolutePathByEntryName[\n              entrypoint.name\n            ]?.replace(this.appDir, '')\n\n          const entryIsStaticMetadataRoute =\n            appDirRelativeEntryPath &&\n            isMetadataRouteFile(appDirRelativeEntryPath, [], true)\n\n          // Include the client reference manifest in the trace, but not for\n          // static metadata routes, for which we don't generate those.\n          if (!entryIsStaticMetadataRoute) {\n            entryFiles.add(\n              nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n            )\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        compilation.emitAsset(\n          traceOutputName,\n          new sources.RawSource(\n            JSON.stringify({\n              version: TRACE_OUTPUT_VERSION,\n              files: finalFiles,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n            const absolutePathByEntryName = new Map<string, string>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                            absolutePathByEntryName.set(name, absolutePath)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              absolutePathByEntryName: Object.fromEntries(\n                absolutePathByEntryName\n              ),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const compilationSpan =\n        getCompilationSpan(compilation) || getCompilationSpan(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (_, callback: any) => {\n            this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["nodePath", "isError", "nodeFileTrace", "CLIENT_REFERENCE_MANIFEST", "TRACE_OUTPUT_VERSION", "webpack", "sources", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "picomatch", "getModuleBuildInfo", "getPageFilePath", "resolveExternal", "isMetadataRouteFile", "getCompilationSpan", "PLUGIN_NAME", "TRACE_IGNORES", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "getFilesMapFromReasons", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "TraceEntryPointsPlugin", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "buildTraceContext", "entryTraces", "tracingRoot", "createTraceAssets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "appDirRelativeEntryPath", "entriesTrace", "absolutePathByEntryName", "replace", "entryIsStaticMetadataRoute", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "emitAsset", "RawSource", "JSON", "stringify", "version", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "route", "absolutePath", "absolutePagePath", "request", "curMap", "readFile", "mod", "source", "originalSource", "buffer", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "chunks", "depModArray", "ignores", "isIgnoreMatcher", "contains", "dot", "traceEntryCount", "result", "base", "resolve", "id", "job", "isCjs", "undefined", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "err", "apply", "compiler", "tap", "compilationSpan", "reject", "inputFileSystem", "link", "e", "code", "stats", "traceFn", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "_", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "options", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resRequest"], "mappings": "AAAA,OAAOA,cAAc,OAAM;AAE3B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,aAAa,QAAQ,iCAAgC;AAE9D,SACEC,yBAAyB,EACzBC,oBAAoB,QAEf,gCAA+B;AACtC,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,wBAAwB,EACxBC,oBAAoB,QACf,uBAAsB;AAE7B,OAAOC,eAAe,+BAA8B;AACpD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,eAAe,QAAQ,gBAAe;AAC/C,SAASC,eAAe,QAAQ,yBAAwB;AACxD,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,kBAAkB,QAAQ,WAAU;AAE7C,MAAMC,cAAc;AACpB,OAAO,MAAMC,gBAAgB;IAC3B;IACA;CACD,CAAA;AAED,MAAMC,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,OAAO,SAASG,uBACdC,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA2BA,OAAO,MAAMsB;IAaXC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EAUtB,CAAE;aA9BIC,oBAAuC,CAAC;QA+B7C,IAAI,CAACR,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACO,WAAW,GAAG,IAAIhC;QACvB,IAAI,CAAC6B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACK,WAAW,GAAGH,yBAAyBP;QAC5C,IAAI,CAACG,YAAY,GAAGA;IACtB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMQ,kBAAkB3C,WAAgC,EAAE4C,IAAU,EAAE;QACpE,MAAMC,aAAa7C,YAAY8C,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAIzC;YAC1B,MAAM0C,gBAAgB,IAAIrC;YAC1B,MAAMsC,oBAAoB,IAAI3C;YAE9B,MAAM4C,cAAc,CAACzC,OACnB,CAACd,cAAcwD,IAAI,CAAC,CAACC;oBACnB,OAAO3C,KAAK4C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAczD,YAAY0D,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI9C;gBAEvB,KAAK,MAAM+C,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAMnD,QAAQiD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWpF,SAASqF,IAAI,CAACrB,YAAYjC;4BAC3CuC,cAAclC,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;oBACA,KAAK,MAAMrD,QAAQiD,MAAMM,cAAc,CAAE;wBACvC,IAAId,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWpF,SAASqF,IAAI,CAACrB,YAAYjC;4BAC3CuC,cAAclC,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;gBACF;gBACAf,cAAc9B,GAAG,CAACqC,YAAYG;gBAC9BR,kBAAkBhC,GAAG,CAACqC,WAAWW,IAAI,IAAI,IAAI;uBAAIR;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACpB,iBAAiB,CAAC6B,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIpB;qBAAc;oBACzBqB,kBAAkB,IAAI,CAAC9B,WAAW;oBAClC+B,YAAY,IAAI,CAACzC,OAAO;gBAC1B;gBACAa;gBACAO,mBAAmBsB,OAAOC,WAAW,CAACvB;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAMwB,eAAe,IAAI,CAACzC,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAACsB,YAAYG,WAAW,IAAIV,cAAe;oBA2C1C;gBA1CV,MAAM2B,kBAAkB,GAAGD,eAAenB,WAAWW,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBjG,SAASkG,OAAO,CACtClG,SAASqF,IAAI,CAACrB,YAAYgC;gBAG5B,8CAA8C;gBAC9CjB,WAAWoB,MAAM,CACfnG,SAASqF,IAAI,CAACrB,YAAY,GAAG+B,eAAenB,WAAWW,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIX,WAAWW,IAAI,CAACa,UAAU,CAAC,WAAW,IAAI,CAAChD,MAAM,EAAE;wBAEnD,8EAAA;oBADF,MAAMiD,2BACJ,uCAAA,IAAI,CAAC1C,iBAAiB,CAAC2C,YAAY,sBAAnC,+EAAA,qCAAqCC,uBAAuB,CAC1D3B,WAAWW,IAAI,CAChB,qBAFD,6EAEGiB,OAAO,CAAC,IAAI,CAACpD,MAAM,EAAE;oBAE1B,MAAMqD,6BACJJ,2BACAxF,oBAAoBwF,yBAAyB,EAAE,EAAE;oBAEnD,kEAAkE;oBAClE,6DAA6D;oBAC7D,IAAI,CAACI,4BAA4B;wBAC/B1B,WAAW3C,GAAG,CACZpC,SAASqF,IAAI,CACXrB,YACA+B,cACAnB,WAAWW,IAAI,CAACiB,OAAO,CAAC,QAAQ,OAC9B,MACArG,4BACA;oBAGR;gBACF;gBAEA,MAAMuG,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAI3E,IAAI;2BACN8C;2BACC,EAAA,wBAAA,IAAI,CAACnB,WAAW,CAACtB,GAAG,CAACsC,WAAWW,IAAI,sBAApC,sBAAuCsB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAO/E;wBACM;oBAAjB,MAAMgF,YAAW,wBAAA,IAAI,CAACnD,WAAW,CAACtB,GAAG,CAACsC,WAAWW,IAAI,sBAApC,sBAAuCjD,GAAG,CAACP;oBAE5D,MAAMiF,eAAehH,SAClBiH,QAAQ,CAAChB,iBAAiBlE,MAC1ByE,OAAO,CAAC,OAAO;oBAElB,IAAIzE,MAAM;wBACR,IAAI,EAACgF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGF7F,YAAYiG,SAAS,CACnBpB,iBACA,IAAI1F,QAAQ+G,SAAS,CACnBC,KAAKC,SAAS,CAAC;oBACbC,SAASpH;oBACT+E,OAAOuB;gBACT;YAGN;QACF;IACF;IAEAe,iBACEtG,WAAgC,EAChCuG,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACA1G,YAAY2G,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCjH,aACA,OAAOkH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2BvD,UAAU,CAAC;YACxC,MAAMgE,kBACH/D,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMgE,eAAe,IAAIxG;gBACzB,MAAMyG,cAAc,IAAIzG;gBACxB,MAAM0G,oBAAoB,IAAI1G;gBAC9B,MAAM2E,0BAA0B,IAAI3E;gBAEpC,MAAM2G,YAAY,IAAI3G;gBAEtB,MAAMuG,kBACHhE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACmB,MAAMiD,MAAM,IAAIrH,YAAYsH,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBnD,wBAAAA,KAAMiB,OAAO,CAAC,OAAO;wBAE5C,MAAMmC,SAASD,eAAetC,UAAU,CAAC;wBACzC,MAAMwC,QACJ,IAAI,CAACrF,aAAa,IAAImF,eAAetC,UAAU,CAAC;wBAElD,IAAIwC,SAASD,QAAQ;4BACnB,KAAK,MAAMvH,OAAOoH,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAACzH,KAAK;gCACV,MAAM0H,WAAW5H,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAI0H,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBtI,mBAAmBoI;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBC,KAAK,EAAE;wCACzB,MAAMC,eAAevI,gBAAgB;4CACnCwI,kBACEH,gBAAgBC,KAAK,CAACE,gBAAgB;4CACxChG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZ6F,aAAa9C,UAAU,CAAC,IAAI,CAAC/C,QAAQ,KACtC,IAAI,CAACD,MAAM,IACV8F,aAAa9C,UAAU,CAAC,IAAI,CAAChD,MAAM,GACrC;4CACAiF,YAAY9F,GAAG,CAAC2G,cAAcJ;4CAC9BV,aAAa7F,GAAG,CAAC2G,cAAc3D;4CAC/BgB,wBAAwBhE,GAAG,CAACgD,MAAM2D;wCACpC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIJ,SAASM,OAAO,EAAE;wCACpB,IAAIC,SAASf,kBAAkBhG,GAAG,CAACiD;wCAEnC,IAAI,CAAC8D,QAAQ;4CACXA,SAAS,IAAIzH;4CACb0G,kBAAkB/F,GAAG,CAACgD,MAAM8D;wCAC9B;wCACAd,UAAUhG,GAAG,CAACuG,SAASM,OAAO,EAAEN;wCAChCO,OAAO9G,GAAG,CAACuG,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAa7F,GAAG,CAACuG,SAASC,QAAQ,EAAExD;oCACpC8C,YAAY9F,GAAG,CAACuG,SAASC,QAAQ,EAAED;oCAEnC,IAAIO,SAASf,kBAAkBhG,GAAG,CAACiD;oCAEnC,IAAI,CAAC8D,QAAQ;wCACXA,SAAS,IAAIzH;wCACb0G,kBAAkB/F,GAAG,CAACgD,MAAM8D;oCAC9B;oCACAd,UAAUhG,GAAG,CAACuG,SAASC,QAAQ,EAAED;oCACjCO,OAAO9G,GAAG,CAACuG,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMQ,WAAW,OACfpF;wBAM8BqF,qBAAAA;oBAJ9B,MAAMA,MAAMhB,UAAUjG,GAAG,CAAC4B,SAASmE,YAAY/F,GAAG,CAAC4B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAIsF,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAC7D,OAAOF,UAAU;gBACnB;gBAEA,MAAMG,aAAaC,MAAMC,IAAI,CAACxB,YAAYxB,IAAI;gBAE9C,MAAMiD,sBAAsB,OAAOP,KAAUrH;oBAC3C,IAAI,CAACqH,OAAO,CAACA,IAAIV,YAAY,EAAE;oBAE/B,KAAK,MAAMzH,OAAOmI,IAAIV,YAAY,CAAE;wBAClC,MAAMkB,SAAS7I,wBAAwBC,aAAaC;wBAEpD,IAAI2I,CAAAA,0BAAAA,OAAQhB,QAAQ,KAAI,CAACR,UAAUjG,GAAG,CAACyH,OAAOhB,QAAQ,GAAG;4BACvDR,UAAUhG,GAAG,CAACwH,OAAOhB,QAAQ,EAAEgB;4BAC/B,MAAMD,oBAAoBC,QAAQ7H;wBACpC;oBACF;gBACF;gBACA,MAAM8H,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAMnB,SAASmB,WAAY;oBAC9B,MAAMG,oBAAoBzB,YAAY/F,GAAG,CAACkG,QAAQA;oBAClD,MAAMyB,YAAY7B,aAAa9F,GAAG,CAACkG;oBACnC,MAAM0B,kBAAkB5B,kBAAkBhG,GAAG,CAAC2H;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAe7C,IAAI,IAAI+C,gBAAgBrD,IAAI;oBAC7C;gBACF;gBAEA,MAAMlB,mBAAmB,IAAI,CAAC9B,WAAW;gBACzC,MAAMsG,SAAS;uBAAIH;iBAAe;gBAElC,IAAI,CAACrG,iBAAiB,CAAC2C,YAAY,GAAG;oBACpCb,QAAQ;wBACNA,QAAQ;wBACRC,OAAOyE;wBACPxE;wBACAC,YAAY,IAAI,CAACzC,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpBiH,aAAaR,MAAMC,IAAI,CAACtB,UAAU1B,IAAI;oBACtCuB,cAAcvC,OAAOC,WAAW,CAACsC;oBACjC7B,yBAAyBV,OAAOC,WAAW,CACzCS;oBAEFvC,YAAY7C,YAAY8C,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAI1C;gBACJ,IAAIC;gBACJ,MAAM4I,UAAU;uBACXrJ;uBACA,IAAI,CAACwC,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAM8G,kBAAkB7J,UAAU4J,SAAS;oBACzCE,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAM9I,WAAW,CAACwC;oBAChB,OAAOoG,gBAAgBpG;gBACzB;gBAEA,MAAMiE,kBACHhE,UAAU,CAAC,0BAA0B;oBACpCsG,iBAAiBT,eAAelH,MAAM,GAAG;gBAC3C,GACCsB,YAAY,CAAC;oBACZ,MAAMsG,SAAS,MAAMxK,cAAc8J,gBAAgB;wBACjDW,MAAM,IAAI,CAAC9G,WAAW;wBACtB+B,YAAY,IAAI,CAACzC,OAAO;wBACxBmG;wBACA1B;wBACAC;wBACA+C,SAASjD,YACL,OAAOkD,IAAI3I,QAAQ4I,KAAKC;4BACtB,OAAOpD,UAAUkD,IAAI3I,QAAQ4I,KAAK,CAACC;wBACrC,IACAC;wBACJC,QAAQvJ;wBACRwJ,cAAc;oBAChB;oBACA,aAAa;oBACb1J,WAAWkJ,OAAOlJ,QAAQ;oBAC1BkJ,OAAOS,WAAW,CAACC,OAAO,CAAC,CAACrJ,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAUiJ,OAAOjJ,OAAO;gBAC1B;gBAEF,MAAM0G,kBACHhE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAMzC,iBAAiBJ,uBACrBC,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAO/B,SAASqF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE9B;wBACvC,MAAMgI,SAASxB,UAAUjG,GAAG,CAACP;wBAC7B,MAAMsJ,WAAU5J,eAAAA,QACba,GAAG,CAACtC,SAASiH,QAAQ,CAAC,IAAI,CAACpD,WAAW,EAAE9B,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACsI,WACDzB,MAAM0B,OAAO,CAACvB,0BAAAA,OAAQwB,OAAO,KAC7BxB,OAAOwB,OAAO,CAACzI,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAM0F,SAASmB,WAAY;4BAeJhI;wBAd1B,MAAMsI,YAAY7B,aAAa9F,GAAG,CAACkG;wBACnC,MAAMgD,kBAAkBxL,SAASiH,QAAQ,CACvC,IAAI,CAACpD,WAAW,EAChB2E;wBAEF,MAAM0B,kBAAkB5B,kBAAkBhG,GAAG,CAAC2H;wBAC9C,MAAMwB,YAAY,IAAI7J;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClB6J,UAAUlJ,GAAG,CAACiG,OAAO;4BACnBtB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAAC9F,KAAKsK,KAAK,IAAI/J,EAAAA,sBAAAA,eACvBW,GAAG,CAACkJ,qCADmB7J,oBAEtB8G,OAAO,OAAM,EAAE,CAAE;4BACnBgD,UAAUlJ,GAAG,CAACvC,SAASqF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAEzC,MAAM;gCAClD8F,SAASwE,KAAKlJ,OAAO;4BACvB;wBACF;wBAEA,IAAI0H,iBAAiB;4BACnB,KAAK,MAAMyB,cAAczB,gBAAgBrD,IAAI,GAAI;oCAOrBlF;gCAN1B,MAAMiK,uBAAuB5L,SAASiH,QAAQ,CAC5C,IAAI,CAACpD,WAAW,EAChB8H;gCAEFF,UAAUlJ,GAAG,CAACoJ,YAAY;oCAAEzE,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAAC9F,KAAKsK,KAAK,IAAI/J,EAAAA,uBAAAA,eACvBW,GAAG,CAACsJ,0CADmBjK,qBAEtB8G,OAAO,OAAM,EAAE,CAAE;oCACnBgD,UAAUlJ,GAAG,CAACvC,SAASqF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAEzC,MAAM;wCAClD8F,SAASwE,KAAKlJ,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAACoB,WAAW,CAACrB,GAAG,CAAC0H,WAAWwB;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAM3D,YACN,CAAC4D,MAAQ5D,SAAS4D;QAExB;IAEJ;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASlE,KAAK,CAAC3G,WAAW,CAAC8K,GAAG,CAAClL,aAAa,CAACI;YAC3C,MAAM+K,kBACJpL,mBAAmBK,gBAAgBL,mBAAmBkL;YACxD,MAAMtE,6BAA6BwE,gBAAgB/H,UAAU,CAC3D;YAGF,MAAMyD,WAAW,OAAO1D;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAACiE,SAASuB;;wBAE/BhL,YAAYiL,eAAe,CACxBxE,QAAQ,CACX1D,MAAM,CAAC4H,KAAKO;4BACZ,IAAIP,KAAK,OAAOK,OAAOL;4BACvBlB,QAAQyB;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACErM,QAAQqM,MACPA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YACA,MAAMzE,OAAO,OAAO3D;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAACiE,SAASuB;;wBAC/BhL,YAAYiL,eAAe,CAACvE,IAAI,CAChC3D,MACA,CAAC4H,KAAKU;4BACJ,IAAIV,KAAK,OAAOK,OAAOL;4BACvBlB,QAAQ4B;wBACV;oBAEJ;gBACF,EAAE,OAAOF,GAAG;oBACV,IAAIrM,QAAQqM,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEA5E,2BAA2B+E,OAAO,CAAC;gBACjCtL,YAAY2G,KAAK,CAAC4E,aAAa,CAAC1E,QAAQ,CACtC;oBACEzC,MAAMxE;oBACN4L,OAAOtM,QAAQuM,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACC,GAAG5E;oBACF,IAAI,CAACpE,iBAAiB,CAAC3C,aAAauG,4BACjCmE,IAAI,CAAC,IAAM3D,YACX6E,KAAK,CAAC,CAACjB,MAAQ5D,SAAS4D;gBAC7B;gBAGF,IAAIkB,WAAW7L,YAAY8L,eAAe,CAAC3K,GAAG,CAAC;gBAE/C,SAAS4K,WAAW3H,IAAY;oBAC9B,MAAM4H,WAAW5H,KAAK6H,KAAK,CAAC;oBAC5B,IAAI7H,IAAI,CAAC,EAAE,KAAK,OAAO4H,SAASrK,MAAM,GAAG,GACvC,OAAOqK,SAASrK,MAAM,GAAG,IAAIqK,SAASE,KAAK,CAAC,GAAG,GAAGhI,IAAI,CAAC,OAAO;oBAChE,OAAO8H,SAASrK,MAAM,GAAGqK,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjBC;oBAEA,MAAMC,cAAcR,SAASS,WAAW,CAACF;oBAEzC,OAAO,CACLrL,QACAkH,SACA0B,MAEA,IAAInE,QAA2B,CAACiE,SAASuB;4BACvC,MAAMuB,UAAU1N,SAASkG,OAAO,CAAChE;4BAEjCsL,YAAY5C,OAAO,CACjB,CAAC,GACD8C,SACAtE,SACA;gCACEuE,kBAAkBxM,YAAYwM,gBAAgB;gCAC9CC,qBAAqBzM,YAAYyM,mBAAmB;gCACpDC,qBAAqB1M,YAAY0M,mBAAmB;4BACtD,GACA,OAAO/B,KAAUpB,QAASoD;gCACxB,IAAIhC,KAAK,OAAOK,OAAOL;gCAEvB,IAAI,CAACpB,QAAQ;oCACX,OAAOyB,OAAO,qBAA6B,CAA7B,IAAI4B,MAAM,qBAAV,qBAAA;+CAAA;oDAAA;sDAAA;oCAA4B;gCAC5C;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAIrD,OAAO3H,QAAQ,CAAC,QAAQ2H,OAAO3H,QAAQ,CAAC,MAAM;oCAChD2H,SAASoD,CAAAA,8BAAAA,WAAY5J,IAAI,KAAIwG;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAO3H,QAAQ,CAAC,iBAAiB;wCACnC,IAAIiL,cAActD,OACflE,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACxG,SAASiO,UAAU,CAAC7E,YACrBA,QAAQrG,QAAQ,CAAC,SACjB+K,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBhB;4CAFhBc,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9B9E,QAAQiE,KAAK,CAACH,EAAAA,cAAAA,WAAW9D,6BAAX8D,YAAqBpK,MAAM,KAAI,KAC7C9C,SAASmO,GAAG,GACZ,cAAa,EAEZ3H,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAM4H,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYX,KAAK,CAAC,GAAGiB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAMlD,IAAI2D,MAAM,CAACD,qBAAqB;gDACxC,MAAM1D,IAAI4D,QAAQ,CAChB,MAAM5D,IAAI6D,QAAQ,CAACH,qBACnB,WACAtM;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAO0M,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACAhE,QAAQ;oCAACF;oCAAQ6C,QAAQsB,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGtO,oBAAoB;oBACvBuO,gBAAgB/D;oBAChBgE,SAAShE;oBACTiE,YAAYjE;gBACd;gBACA,MAAMkE,2BAA2B;oBAC/B,GAAGJ,mBAAmB;oBACtBK,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAG7O,wBAAwB;oBAC3BwO,gBAAgB/D;oBAChBgE,SAAShE;oBACTiE,YAAYjE;gBACd;gBACA,MAAMqE,2BAA2B;oBAC/B,GAAGD,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAMxH,YAAY,OAChByB,SACAlH,QACA4I,KACAwE;oBAEA,MAAM5B,UAAU1N,SAASkG,OAAO,CAAChE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEqN,GAAG,EAAE,GAAG,MAAM3O,gBACpB,IAAI,CAACuC,OAAO,EACZ,IAAI,CAACM,YAAY,EACjBiK,SACAtE,SACAkG,gBACA,CAAC/B,UAAY,CAACT,GAAW0C;4BACvB,OAAOlC,WAAWC,SAASrL,QAAQsN,YAAY1E;wBACjD,GACAE,WACAA,WACAoE,qBACAN,qBACAO,0BACAH;oBAGF,IAAI,CAACK,KAAK;wBACR,MAAM,qBAAwD,CAAxD,IAAIxB,MAAM,CAAC,kBAAkB,EAAE3E,QAAQ,MAAM,EAAElH,QAAQ,GAAvD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAuD;oBAC/D;oBACA,OAAOqN,IAAI/I,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACiB,gBAAgB,CACnBtG,aACAuG,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF"}