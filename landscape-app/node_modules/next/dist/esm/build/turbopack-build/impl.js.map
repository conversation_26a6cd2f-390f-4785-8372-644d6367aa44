{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport {\n  rawEntrypointsToEntrypoints,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: dir,\n      rootPath: config.turbopack?.root || config.outputFileTracingRoot || dir,\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental?.turbopackMemoryLimit,\n      dependencyTracking: persistentCaching,\n    }\n  )\n  try {\n    // Write an empty file in a known location to signal this was built with Turbopack\n    await fs.writeFile(path.join(distDir, 'turbopack'), '')\n\n    await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n    await fs.mkdir(path.join(distDir, 'static', buildId), {\n      recursive: true,\n    })\n    await fs.writeFile(\n      path.join(distDir, 'package.json'),\n      JSON.stringify(\n        {\n          type: 'commonjs',\n        },\n        null,\n        2\n      )\n    )\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const entrypoints = await project.writeAllEntrypointsToDisk(appDirOnly)\n\n    const manifestLoader = new TurbopackManifestLoader({\n      buildId,\n      distDir,\n      encryptionKey,\n    })\n\n    const topLevelErrors = []\n    const topLevelWarnings = []\n    for (const issue of entrypoints.issues) {\n      if (issue.severity === 'error' || issue.severity === 'fatal') {\n        topLevelErrors.push(formatIssue(issue))\n      } else if (isRelevantWarning(issue)) {\n        topLevelWarnings.push(formatIssue(issue))\n      }\n    }\n\n    if (topLevelWarnings.length > 0) {\n      console.warn(\n        `Turbopack build encountered ${\n          topLevelWarnings.length\n        } warnings:\\n${topLevelWarnings.join('\\n')}`\n      )\n    }\n\n    if (topLevelErrors.length > 0) {\n      throw new Error(\n        `Turbopack build failed with ${\n          topLevelErrors.length\n        } errors:\\n${topLevelErrors.join('\\n')}`\n      )\n    }\n\n    const currentEntrypoints = await rawEntrypointsToEntrypoints(entrypoints)\n\n    const promises: Promise<any>[] = []\n\n    if (!appDirOnly) {\n      for (const [page, route] of currentEntrypoints.page) {\n        promises.push(\n          handleRouteType({\n            page,\n            route,\n            manifestLoader,\n          })\n        )\n      }\n    }\n\n    for (const [page, route] of currentEntrypoints.app) {\n      promises.push(\n        handleRouteType({\n          page,\n          route,\n          manifestLoader,\n        })\n      )\n    }\n\n    await Promise.all(promises)\n\n    await Promise.all([\n      manifestLoader.loadBuildManifest('_app'),\n      manifestLoader.loadPagesManifest('_app'),\n      manifestLoader.loadFontManifest('_app'),\n      manifestLoader.loadPagesManifest('_document'),\n      manifestLoader.loadBuildManifest('_error'),\n      manifestLoader.loadPagesManifest('_error'),\n      manifestLoader.loadFontManifest('_error'),\n      entrypoints.instrumentation &&\n        manifestLoader.loadMiddlewareManifest(\n          'instrumentation',\n          'instrumentation'\n        ),\n      entrypoints.middleware &&\n        (await manifestLoader.loadMiddlewareManifest(\n          'middleware',\n          'middleware'\n        )),\n    ])\n\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites: rewrites,\n      entrypoints: currentEntrypoints,\n    })\n\n    const shutdownPromise = project.shutdown()\n\n    const time = process.hrtime(startTime)\n    return {\n      duration: time[0] + time[1] / 1e9,\n      buildTraceContext: undefined,\n      shutdownPromise,\n    }\n  } catch (err) {\n    await project.shutdown()\n    throw err\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["path", "validateTurboNextConfig", "formatIssue", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isRelevantWarning", "NextBuildContext", "createDefineEnv", "loadBindings", "rawEntrypointsToEntrypoints", "handleRouteType", "TurbopackManifestLoader", "promises", "fs", "PHASE_PRODUCTION_BUILD", "loadConfig", "hasCustomExportOutput", "Telemetry", "setGlobal", "turbopackBuild", "config", "dir", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "startTime", "process", "hrtime", "bindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "persistentCaching", "project", "turbo", "createProject", "projectPath", "rootPath", "turbopack", "root", "outputFileTracingRoot", "nextConfig", "jsConfig", "watch", "enable", "env", "defineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "turbopackMemoryLimit", "dependencyTracking", "writeFile", "mkdir", "recursive", "JSON", "stringify", "type", "entrypoints", "writeAllEntrypointsToDisk", "manifest<PERSON><PERSON>der", "topLevelErrors", "topLevelWarnings", "issue", "issues", "severity", "push", "length", "console", "warn", "Error", "currentEntrypoints", "page", "route", "app", "Promise", "all", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "instrumentation", "loadMiddlewareManifest", "middleware", "writeManifests", "devRewrites", "productionRewrites", "shutdownPromise", "shutdown", "time", "duration", "buildTraceContext", "err", "worker<PERSON>ain", "workerData", "Object", "assign", "buildContext", "telemetry", "result", "waitForShutdown"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SACEC,WAAW,EACXC,oBAAoB,EACpBC,0BAA0B,EAC1BC,iBAAiB,QACZ,mCAAkC;AACzC,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,eAAe,EAAEC,YAAY,QAAQ,SAAQ;AACtD,SACEC,2BAA2B,EAC3BC,eAAe,QACV,wBAAuB;AAC9B,SAASC,uBAAuB,QAAQ,6CAA4C;AACpF,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,sBAAsB,QAAQ,6BAA4B;AACnE,OAAOC,gBAAgB,sBAAqB;AAC5C,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,SAAS,QAAQ,cAAa;AAEvC,OAAO,eAAeC;QAsBgBC,sBAYtBA,mBA4BGA;IAzDjB,MAAMnB,wBAAwB;QAC5BoB,KAAKf,iBAAiBe,GAAG;QACzBC,OAAO;IACT;IAEA,MAAMF,SAASd,iBAAiBc,MAAM;IACtC,MAAMC,MAAMf,iBAAiBe,GAAG;IAChC,MAAME,UAAUjB,iBAAiBiB,OAAO;IACxC,MAAMC,UAAUlB,iBAAiBkB,OAAO;IACxC,MAAMC,gBAAgBnB,iBAAiBmB,aAAa;IACpD,MAAMC,eAAepB,iBAAiBoB,YAAY;IAClD,MAAMC,cAAcrB,iBAAiBqB,WAAW;IAChD,MAAMC,WAAWtB,iBAAiBsB,QAAQ;IAC1C,MAAMC,aAAavB,iBAAiBuB,UAAU;IAC9C,MAAMC,aAAaxB,iBAAiBwB,UAAU;IAE9C,MAAMC,YAAYC,QAAQC,MAAM;IAChC,MAAMC,WAAW,MAAM1B,aAAaY,2BAAAA,uBAAAA,OAAQe,YAAY,qBAApBf,qBAAsBgB,aAAa;IACvE,MAAMC,MAAM;IAEZ,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMC,oBAAoBnC,2BAA2BgB;IACrD,MAAMoB,UAAU,MAAMN,SAASO,KAAK,CAACC,aAAa,CAChD;QACEC,aAAatB;QACbuB,UAAUxB,EAAAA,oBAAAA,OAAOyB,SAAS,qBAAhBzB,kBAAkB0B,IAAI,KAAI1B,OAAO2B,qBAAqB,IAAI1B;QACpEE;QACAyB,YAAY5B;QACZ6B,UAAU,MAAM9C,qBAAqBkB,KAAKD;QAC1C8B,OAAO;YACLC,QAAQ;QACV;QACAd;QACAe,KAAKpB,QAAQoB,GAAG;QAChBC,WAAW9C,gBAAgB;YACzB+C,aAAa;YACbC,qBAAqBjD,iBAAiBiD,mBAAmB;YACzDnC;YACAiB;YACAd;YACAiC,qBAAqBpC,OAAOe,YAAY,CAACqB,mBAAmB;YAC5D7B;YACA,uEAAuE;YACvE8B,oBAAoBC;QACtB;QACAlC;QACAC;QACAC;QACAiC,mBAAmBrB,kBAAkBsB,IAAI,CAAC;QAC1C9B;IACF,GACA;QACES;QACAsB,WAAW,GAAEzC,wBAAAA,OAAOe,YAAY,qBAAnBf,sBAAqB0C,oBAAoB;QACtDC,oBAAoBxB;IACtB;IAEF,IAAI;QACF,kFAAkF;QAClF,MAAM1B,GAAGmD,SAAS,CAAChE,KAAK4D,IAAI,CAACrC,SAAS,cAAc;QAEpD,MAAMV,GAAGoD,KAAK,CAACjE,KAAK4D,IAAI,CAACrC,SAAS,WAAW;YAAE2C,WAAW;QAAK;QAC/D,MAAMrD,GAAGoD,KAAK,CAACjE,KAAK4D,IAAI,CAACrC,SAAS,UAAUC,UAAU;YACpD0C,WAAW;QACb;QACA,MAAMrD,GAAGmD,SAAS,CAChBhE,KAAK4D,IAAI,CAACrC,SAAS,iBACnB4C,KAAKC,SAAS,CACZ;YACEC,MAAM;QACR,GACA,MACA;QAIJ,6DAA6D;QAC7D,MAAMC,cAAc,MAAM9B,QAAQ+B,yBAAyB,CAAC1C;QAE5D,MAAM2C,iBAAiB,IAAI7D,wBAAwB;YACjDa;YACAD;YACAE;QACF;QAEA,MAAMgD,iBAAiB,EAAE;QACzB,MAAMC,mBAAmB,EAAE;QAC3B,KAAK,MAAMC,SAASL,YAAYM,MAAM,CAAE;YACtC,IAAID,MAAME,QAAQ,KAAK,WAAWF,MAAME,QAAQ,KAAK,SAAS;gBAC5DJ,eAAeK,IAAI,CAAC5E,YAAYyE;YAClC,OAAO,IAAItE,kBAAkBsE,QAAQ;gBACnCD,iBAAiBI,IAAI,CAAC5E,YAAYyE;YACpC;QACF;QAEA,IAAID,iBAAiBK,MAAM,GAAG,GAAG;YAC/BC,QAAQC,IAAI,CACV,CAAC,4BAA4B,EAC3BP,iBAAiBK,MAAM,CACxB,YAAY,EAAEL,iBAAiBd,IAAI,CAAC,OAAO;QAEhD;QAEA,IAAIa,eAAeM,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAIL,CAJK,IAAIG,MACR,CAAC,4BAA4B,EAC3BT,eAAeM,MAAM,CACtB,UAAU,EAAEN,eAAeb,IAAI,CAAC,OAAO,GAHpC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QAEA,MAAMuB,qBAAqB,MAAM1E,4BAA4B6D;QAE7D,MAAM1D,WAA2B,EAAE;QAEnC,IAAI,CAACiB,YAAY;YACf,KAAK,MAAM,CAACuD,MAAMC,MAAM,IAAIF,mBAAmBC,IAAI,CAAE;gBACnDxE,SAASkE,IAAI,CACXpE,gBAAgB;oBACd0E;oBACAC;oBACAb;gBACF;YAEJ;QACF;QAEA,KAAK,MAAM,CAACY,MAAMC,MAAM,IAAIF,mBAAmBG,GAAG,CAAE;YAClD1E,SAASkE,IAAI,CACXpE,gBAAgB;gBACd0E;gBACAC;gBACAb;YACF;QAEJ;QAEA,MAAMe,QAAQC,GAAG,CAAC5E;QAElB,MAAM2E,QAAQC,GAAG,CAAC;YAChBhB,eAAeiB,iBAAiB,CAAC;YACjCjB,eAAekB,iBAAiB,CAAC;YACjClB,eAAemB,gBAAgB,CAAC;YAChCnB,eAAekB,iBAAiB,CAAC;YACjClB,eAAeiB,iBAAiB,CAAC;YACjCjB,eAAekB,iBAAiB,CAAC;YACjClB,eAAemB,gBAAgB,CAAC;YAChCrB,YAAYsB,eAAe,IACzBpB,eAAeqB,sBAAsB,CACnC,mBACA;YAEJvB,YAAYwB,UAAU,IACnB,MAAMtB,eAAeqB,sBAAsB,CAC1C,cACA;SAEL;QAED,MAAMrB,eAAeuB,cAAc,CAAC;YAClCC,aAAatC;YACbuC,oBAAoBrE;YACpB0C,aAAaa;QACf;QAEA,MAAMe,kBAAkB1D,QAAQ2D,QAAQ;QAExC,MAAMC,OAAOpE,QAAQC,MAAM,CAACF;QAC5B,OAAO;YACLsE,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;YAC9BE,mBAAmB5C;YACnBwC;QACF;IACF,EAAE,OAAOK,KAAK;QACZ,MAAM/D,QAAQ2D,QAAQ;QACtB,MAAMI;IACR;AACF;AAEA,IAAIL;AACJ,OAAO,eAAeM,WAAWC,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAACrG,kBAAkBmG,WAAWG,YAAY;IAEvD,iDAAiD;IACjDtG,iBAAiBc,MAAM,GAAG,MAAML,WAC9BD,wBACAR,iBAAiBe,GAAG;IAGtB,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAIL,sBAAsBV,iBAAiBc,MAAM,GAAG;QAClDd,iBAAiBc,MAAM,CAACG,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAMsF,YAAY,IAAI5F,UAAU;QAC9BM,SAASjB,iBAAiBc,MAAM,CAACG,OAAO;IAC1C;IACAL,UAAU,aAAa2F;IAEvB,MAAMC,SAAS,MAAM3F;IACrB+E,kBAAkBY,OAAOZ,eAAe;IACxC,OAAOY;AACT;AAEA,OAAO,eAAeC;IACpB,IAAIb,iBAAiB;QACnB,MAAMA;IACR;AACF"}