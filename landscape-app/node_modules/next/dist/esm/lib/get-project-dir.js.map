{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "sourcesContent": ["import path from 'path'\nimport { warn } from '../build/output/log'\nimport { detectTypo } from './detect-typo'\nimport { realpathSync } from './realpath'\nimport { printAndExit } from '../server/lib/utils'\n\nexport function getProjectDir(dir?: string, exitOnEnoent = true) {\n  const resolvedDir = path.resolve(dir || '.')\n  try {\n    const realDir = realpathSync(resolvedDir)\n\n    if (\n      resolvedDir !== realDir &&\n      resolvedDir.toLowerCase() === realDir.toLowerCase()\n    ) {\n      warn(\n        `Invalid casing detected for project dir, received ${resolvedDir} actual path ${realDir}, see more info here https://nextjs.org/docs/messages/invalid-project-dir-casing`\n      )\n    }\n\n    return realDir\n  } catch (err: any) {\n    if (err.code === 'ENOENT' && exitOnEnoent) {\n      if (typeof dir === 'string') {\n        const detectedTypo = detectTypo(dir, [\n          'build',\n          'dev',\n          'info',\n          'lint',\n          'start',\n          'telemetry',\n          'experimental-test',\n        ])\n\n        if (detectedTypo) {\n          return printAndExit(\n            `\"next ${dir}\" does not exist. Did you mean \"next ${detectedTypo}\"?`\n          )\n        }\n      }\n\n      return printAndExit(\n        `Invalid project directory provided, no such directory: ${resolvedDir}`\n      )\n    }\n    throw err\n  }\n}\n"], "names": ["path", "warn", "detectTypo", "realpathSync", "printAndExit", "getProjectDir", "dir", "exitOnEnoent", "resolvedDir", "resolve", "realDir", "toLowerCase", "err", "code", "detectedTypo"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,IAAI,QAAQ,sBAAqB;AAC1C,SAASC,UAAU,QAAQ,gBAAe;AAC1C,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,YAAY,QAAQ,sBAAqB;AAElD,OAAO,SAASC,cAAcC,GAAY,EAAEC,eAAe,IAAI;IAC7D,MAAMC,cAAcR,KAAKS,OAAO,CAACH,OAAO;IACxC,IAAI;QACF,MAAMI,UAAUP,aAAaK;QAE7B,IACEA,gBAAgBE,WAChBF,YAAYG,WAAW,OAAOD,QAAQC,WAAW,IACjD;YACAV,KACE,CAAC,kDAAkD,EAAEO,YAAY,aAAa,EAAEE,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOE,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,YAAYN,cAAc;YACzC,IAAI,OAAOD,QAAQ,UAAU;gBAC3B,MAAMQ,eAAeZ,WAAWI,KAAK;oBACnC;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,IAAIQ,cAAc;oBAChB,OAAOV,aACL,CAAC,MAAM,EAAEE,IAAI,qCAAqC,EAAEQ,aAAa,EAAE,CAAC;gBAExE;YACF;YAEA,OAAOV,aACL,CAAC,uDAAuD,EAAEI,aAAa;QAE3E;QACA,MAAMI;IACR;AACF"}