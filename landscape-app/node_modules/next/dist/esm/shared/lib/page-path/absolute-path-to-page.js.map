{"version": 3, "sources": ["../../../../src/shared/lib/page-path/absolute-path-to-page.ts"], "sourcesContent": ["import { ensureLeadingSlash } from './ensure-leading-slash'\nimport { normalizePathSep } from './normalize-path-sep'\nimport path from '../isomorphic/path'\nimport { removePagePathTail } from './remove-page-path-tail'\nimport { normalizeMetadataRoute } from '../../../lib/metadata/get-metadata-route'\nimport type { PAGE_TYPES } from '../../../lib/page-types'\n\n/**\n * Given the absolute path to the pages folder, an absolute file path for a\n * page and the page extensions, this function will return the page path\n * relative to the pages folder. It doesn't consider index tail. Example:\n *  - `/Users/<USER>/my-project/pages/foo/bar/baz.js` -> `/foo/bar/baz`\n *\n * It also handles special metadata routes mapping. Example:\n * - `/Users/<USER>/my-project/app/sitemap.js` -> `/sitemap/route`\n *\n * @param filepath Absolute path to the page.\n * @param opts.dir Absolute path to the pages/app folder.\n * @param opts.extensions Extensions allowed for the page.\n * @param opts.keepIndex When true the trailing `index` kept in the path.\n * @param opts.pagesType Whether the page is in the pages or app directory.\n */\nexport function absolutePathToPage(\n  pagePath: string,\n  options: {\n    extensions: string[] | readonly string[]\n    keepIndex: boolean\n    dir: string\n    pagesType: PAGE_TYPES\n  }\n) {\n  const isAppDir = options.pagesType === 'app'\n  const page = removePagePathTail(\n    normalizePathSep(ensureLeadingSlash(path.relative(options.dir, pagePath))),\n    {\n      extensions: options.extensions,\n      keepIndex: options.keepIndex,\n    }\n  )\n  return isAppDir ? normalizeMetadataRoute(page) : page\n}\n"], "names": ["ensureLeadingSlash", "normalizePathSep", "path", "removePagePathTail", "normalizeMetadataRoute", "absolutePathToPage", "pagePath", "options", "isAppDir", "pagesType", "page", "relative", "dir", "extensions", "keepIndex"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,OAAOC,UAAU,qBAAoB;AACrC,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,sBAAsB,QAAQ,2CAA0C;AAGjF;;;;;;;;;;;;;;CAcC,GACD,OAAO,SAASC,mBACdC,QAAgB,EAChBC,OAKC;IAED,MAAMC,WAAWD,QAAQE,SAAS,KAAK;IACvC,MAAMC,OAAOP,mBACXF,iBAAiBD,mBAAmBE,KAAKS,QAAQ,CAACJ,QAAQK,GAAG,EAAEN,aAC/D;QACEO,YAAYN,QAAQM,UAAU;QAC9BC,WAAWP,QAAQO,SAAS;IAC9B;IAEF,OAAON,WAAWJ,uBAAuBM,QAAQA;AACnD"}