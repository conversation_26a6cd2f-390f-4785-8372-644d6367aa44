{"version": 3, "sources": ["../../../src/shared/lib/amp.ts"], "sourcesContent": ["import React from 'react'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\n\nexport function useAmp(): boolean {\n  // Don't assign the context value to a variable to save bytes\n  return isInAmpMode(React.useContext(AmpStateContext))\n}\n"], "names": ["React", "AmpStateContext", "isInAmpMode", "useAmp", "useContext"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,WAAW,QAAQ,aAAY;AAExC,OAAO,SAASC;IACd,6DAA6D;IAC7D,OAAOD,YAAYF,MAAMI,UAAU,CAACH;AACtC"}