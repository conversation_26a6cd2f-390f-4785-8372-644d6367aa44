{"version": 3, "sources": ["../../../src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "value", "Object", "prototype", "toString", "call", "isPlainObject", "getPrototypeOf", "hasOwnProperty"], "mappings": "AAAA,OAAO,SAASA,oBAAoBC,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEA,OAAO,SAASK,cAAcL,KAAU;IACtC,IAAID,oBAAoBC,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOK,cAAc,CAACN;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUK,cAAc,CAAC;AACxD"}