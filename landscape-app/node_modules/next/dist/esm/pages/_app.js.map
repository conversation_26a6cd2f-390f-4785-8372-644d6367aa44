{"version": 3, "sources": ["../../src/pages/_app.tsx"], "sourcesContent": ["import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n"], "names": ["React", "loadGetInitialProps", "appGetInitialProps", "Component", "ctx", "pageProps", "App", "render", "props", "origGetInitialProps", "getInitialProps"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAWzB,SAASC,mBAAmB,QAAQ,sBAAqB;AAUzD;;;CAGC,GACD,eAAeC,mBAAmB,KAGrB;IAHqB,IAAA,EAChCC,SAAS,EACTC,GAAG,EACQ,GAHqB;IAIhC,MAAMC,YAAY,MAAMJ,oBAAoBE,WAAWC;IACvD,OAAO;QAAEC;IAAU;AACrB;AAEe,MAAMC,YAAsCN,MAAMG,SAAS;IAOxEI,SAAS;QACP,MAAM,EAAEJ,SAAS,EAAEE,SAAS,EAAE,GAAG,IAAI,CAACG,KAAK;QAE3C,qBAAO,KAACL;YAAW,GAAGE,SAAS;;IACjC;AACF;AAZqBC,IAIZG,sBAAsBP;AAJVI,IAKZI,kBAAkBR;AAL3B,SAAqBI,iBAYpB"}