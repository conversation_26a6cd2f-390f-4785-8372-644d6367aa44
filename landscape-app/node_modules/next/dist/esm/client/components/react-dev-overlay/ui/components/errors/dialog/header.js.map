{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dialog/header.tsx"], "sourcesContent": ["import { DialogHeader } from '../../dialog/dialog-header'\n\ntype ErrorOverlayDialogHeaderProps = {\n  children?: React.ReactNode\n}\n\nexport function ErrorOverlayDialogHeader({\n  children,\n}: ErrorOverlayDialogHeaderProps) {\n  return (\n    <DialogHeader className=\"nextjs-container-errors-header\">\n      {children}\n    </DialogHeader>\n  )\n}\n\nexport const DIALOG_HEADER_STYLES = `\n  .nextjs-container-errors-header {\n    position: relative;\n  }\n  .nextjs-container-errors-header > h1 {\n    font-size: var(--size-20);\n    line-height: var(--size-24);\n    font-weight: bold;\n    margin: calc(16px * 1.5) 0;\n    color: var(--color-title-h1);\n  }\n  .nextjs-container-errors-header small {\n    font-size: var(--size-14);\n    color: var(--color-accents-1);\n    margin-left: 16px;\n  }\n  .nextjs-container-errors-header small > span {\n    font-family: var(--font-stack-monospace);\n  }\n  .nextjs-container-errors-header > div > small {\n    margin: 0;\n    margin-top: 4px;\n  }\n  .nextjs-container-errors-header > p > a {\n    color: inherit;\n    font-weight: bold;\n  }\n  .nextjs-container-errors-header\n    > .nextjs-container-build-error-version-status {\n    position: absolute;\n    top: 16px;\n    right: 16px;\n  }\n`\n"], "names": ["DialogHeader", "ErrorOverlayDialogHeader", "children", "className", "DIALOG_HEADER_STYLES"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,6BAA4B;AAMzD,OAAO,SAASC,yBAAyB,KAET;IAFS,IAAA,EACvCC,QAAQ,EACsB,GAFS;IAGvC,qBACE,KAACF;QAAaG,WAAU;kBACrBD;;AAGP;AAEA,OAAO,MAAME,uBAAwB,61BAiCpC"}