{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "sourcesContent": ["import { computeChangedPath } from './compute-changed-path'\nimport type {\n  Mu<PERSON>,\n  ReadonlyReducerState,\n  ReducerState,\n} from './router-reducer-types'\n\nfunction isNotUndefined<T>(value: T): value is Exclude<T, undefined> {\n  return typeof value !== 'undefined'\n}\n\nexport function handleMutable(\n  state: ReadonlyReducerState,\n  mutable: Mutable\n): ReducerState {\n  // shouldScroll is true by default, can override to false.\n  const shouldScroll = mutable.shouldScroll ?? true\n\n  let nextUrl = state.nextUrl\n\n  if (isNotUndefined(mutable.patchedTree)) {\n    // If we received a patched tree, we need to compute the changed path.\n    const changedPath = computeChangedPath(state.tree, mutable.patchedTree)\n    if (changedPath) {\n      // If the tree changed, we need to update the nextUrl\n      nextUrl = changedPath\n    } else if (!nextUrl) {\n      // if the tree ends up being the same (ie, no changed path), and we don't have a nextUrl, then we should use the canonicalUrl\n      nextUrl = state.canonicalUrl\n    }\n    // otherwise this will be a no-op and continue to use the existing nextUrl\n  }\n\n  return {\n    // Set href.\n    canonicalUrl: isNotUndefined(mutable.canonicalUrl)\n      ? mutable.canonicalUrl === state.canonicalUrl\n        ? state.canonicalUrl\n        : mutable.canonicalUrl\n      : state.canonicalUrl,\n    pushRef: {\n      pendingPush: isNotUndefined(mutable.pendingPush)\n        ? mutable.pendingPush\n        : state.pushRef.pendingPush,\n      mpaNavigation: isNotUndefined(mutable.mpaNavigation)\n        ? mutable.mpaNavigation\n        : state.pushRef.mpaNavigation,\n      preserveCustomHistoryState: isNotUndefined(\n        mutable.preserveCustomHistoryState\n      )\n        ? mutable.preserveCustomHistoryState\n        : state.pushRef.preserveCustomHistoryState,\n    },\n    // All navigation requires scroll and focus management to trigger.\n    focusAndScrollRef: {\n      apply: shouldScroll\n        ? isNotUndefined(mutable?.scrollableSegments)\n          ? true\n          : state.focusAndScrollRef.apply\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          false,\n      onlyHashChange: mutable.onlyHashChange || false,\n      hashFragment: shouldScroll\n        ? // Empty hash should trigger default behavior of scrolling layout into view.\n          // #top is handled in layout-router.\n          mutable.hashFragment && mutable.hashFragment !== ''\n          ? // Remove leading # and decode hash to make non-latin hashes work.\n            decodeURIComponent(mutable.hashFragment.slice(1))\n          : state.focusAndScrollRef.hashFragment\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          null,\n      segmentPaths: shouldScroll\n        ? mutable?.scrollableSegments ?? state.focusAndScrollRef.segmentPaths\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          [],\n    },\n    // Apply cache.\n    cache: mutable.cache ? mutable.cache : state.cache,\n    prefetchCache: mutable.prefetchCache\n      ? mutable.prefetchCache\n      : state.prefetchCache,\n    // Apply patched router state.\n    tree: isNotUndefined(mutable.patchedTree)\n      ? mutable.patchedTree\n      : state.tree,\n    nextUrl,\n  }\n}\n"], "names": ["computeChangedPath", "isNotUndefined", "value", "handleMutable", "state", "mutable", "shouldScroll", "nextUrl", "patchedTree", "changedPath", "tree", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAO3D,SAASC,eAAkBC,KAAQ;IACjC,OAAO,OAAOA,UAAU;AAC1B;AAEA,OAAO,SAASC,cACdC,KAA2B,EAC3BC,OAAgB;QAGKA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;IAE7C,IAAIE,UAAUH,MAAMG,OAAO;IAE3B,IAAIN,eAAeI,QAAQG,WAAW,GAAG;QACvC,sEAAsE;QACtE,MAAMC,cAAcT,mBAAmBI,MAAMM,IAAI,EAAEL,QAAQG,WAAW;QACtE,IAAIC,aAAa;YACf,qDAAqD;YACrDF,UAAUE;QACZ,OAAO,IAAI,CAACF,SAAS;YACnB,6HAA6H;YAC7HA,UAAUH,MAAMO,YAAY;QAC9B;IACA,0EAA0E;IAC5E;QAyCQN;IAvCR,OAAO;QACL,YAAY;QACZM,cAAcV,eAAeI,QAAQM,YAAY,IAC7CN,QAAQM,YAAY,KAAKP,MAAMO,YAAY,GACzCP,MAAMO,YAAY,GAClBN,QAAQM,YAAY,GACtBP,MAAMO,YAAY;QACtBC,SAAS;YACPC,aAAaZ,eAAeI,QAAQQ,WAAW,IAC3CR,QAAQQ,WAAW,GACnBT,MAAMQ,OAAO,CAACC,WAAW;YAC7BC,eAAeb,eAAeI,QAAQS,aAAa,IAC/CT,QAAQS,aAAa,GACrBV,MAAMQ,OAAO,CAACE,aAAa;YAC/BC,4BAA4Bd,eAC1BI,QAAQU,0BAA0B,IAEhCV,QAAQU,0BAA0B,GAClCX,MAAMQ,OAAO,CAACG,0BAA0B;QAC9C;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOX,eACHL,eAAeI,2BAAAA,QAASa,kBAAkB,IACxC,OACAd,MAAMY,iBAAiB,CAACC,KAAK,GAE/B;YACJE,gBAAgBd,QAAQc,cAAc,IAAI;YAC1CC,cAAcd,eAEV,oCAAoC;YACpCD,QAAQe,YAAY,IAAIf,QAAQe,YAAY,KAAK,KAE/CC,mBAAmBhB,QAAQe,YAAY,CAACE,KAAK,CAAC,MAC9ClB,MAAMY,iBAAiB,CAACI,YAAY,GAEtC;YACJG,cAAcjB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASa,kBAAkB,YAA3Bb,8BAA+BD,MAAMY,iBAAiB,CAACO,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOnB,QAAQmB,KAAK,GAAGnB,QAAQmB,KAAK,GAAGpB,MAAMoB,KAAK;QAClDC,eAAepB,QAAQoB,aAAa,GAChCpB,QAAQoB,aAAa,GACrBrB,MAAMqB,aAAa;QACvB,8BAA8B;QAC9Bf,MAAMT,eAAeI,QAAQG,WAAW,IACpCH,QAAQG,WAAW,GACnBJ,MAAMM,IAAI;QACdH;IACF;AACF"}