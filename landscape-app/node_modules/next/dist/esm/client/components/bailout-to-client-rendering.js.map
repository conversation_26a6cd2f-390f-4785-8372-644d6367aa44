{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["BailoutToCSRError", "workAsyncStorage", "bailoutToClientRendering", "reason", "workStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,gBAAgB,QAAQ,sDAAqD;AAEtF,OAAO,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,YAAYH,iBAAiBI,QAAQ;IAE3C,IAAID,6BAAAA,UAAWE,WAAW,EAAE;IAE5B,IAAIF,6BAAAA,UAAWG,kBAAkB,EAAE,MAAM,qBAA6B,CAA7B,IAAIP,kBAAkBG,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE"}