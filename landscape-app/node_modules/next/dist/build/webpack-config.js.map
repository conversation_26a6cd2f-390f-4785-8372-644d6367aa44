{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  isWebpackDefaultLayer,\n  isWebpackServerOnlyLayer,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { getDefineEnvPlugin } from './webpack/plugins/define-env-plugin'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createRSCAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackCore, getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nfunction getReactRefreshLoader() {\n  return process.env.NEXT_RSPACK\n    ? getRspackReactRefresh().loader\n    : require.resolve(reactRefreshLoaderName)\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = getReactRefreshLoader()\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    edgePreviewProps,\n    isCompileMode,\n  }: {\n    isCompileMode?: boolean\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    edgePreviewProps?: Record<string, string>\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const bundler = getWebpackBundler()\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const enableTypedRoutes = !!config.experimental.typedRoutes && hasAppDir\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = require('./swc')?.getBinaryMetadata?.()\n      ?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude: (excludePath: string) => {\n      if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n        return false\n      }\n\n      const shouldBeBundled = isResourceInPackages(\n        excludePath,\n        finalTranspilePackages\n      )\n      if (shouldBeBundled) return false\n\n      return excludePath.includes('node_modules')\n    },\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      require('./swc')?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders = dev && isClient ? [getReactRefreshLoader()] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const reactRefreshEntry = isRspack\n    ? getRspackReactRefresh().entry\n    : require.resolve(\n        `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n      )\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: reactRefreshEntry,\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    reactRefreshEntry,\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      isNodeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n      hasRewrites,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = require('module').builtinModules\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          // TODO: investigate these cache groups with rspack\n          cacheGroups: isRspack\n            ? {}\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: isRspack\n        ? [\n            new (getRspackCore().SwcJsMinimizerRspackPlugin)({\n              // JS minimizer configuration\n              // options should align with crates/napi/src/minify.rs#patch_opts\n              minimizerOptions: {\n                compress: {\n                  inline: 2,\n                  global_defs: {\n                    'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                  },\n                },\n                mangle: !noMangling && {\n                  reserved: ['AbortSignal'],\n                  disableCharFreq: !isClient,\n                },\n              },\n            }),\n            new (getRspackCore().LightningCssMinimizerRspackPlugin)({\n              // CSS minimizer configuration\n              minimizerOptions: {\n                targets: supportedBrowsers,\n              },\n            }),\n          ]\n        : [\n            // Minify JavaScript\n            (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src/index.js') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({ noMangling }).apply(compiler)\n            },\n            // Minify CSS\n            (compiler: webpack.Compiler) => {\n              const {\n                CssMinimizerPlugin,\n              } = require('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n          ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : appDir ? '-[chunkhash]' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createRSCAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createRSCAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new bundler.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)({\n              injectLoader: false,\n              injectEntry: false,\n              overlay: false,\n            })\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new bundler.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n      getDefineEnvPlugin({\n        isTurbopack: false,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient,\n        isEdgeServer,\n        isNodeOrEdgeCompilation,\n        isNodeServer,\n        middlewareMatchers,\n        omitNonDeterministic: isCompileMode,\n      }),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new (require('./webpack/plugins/next-trace-entrypoints-plugin')\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new bundler.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            ...edgePreviewProps,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          typedRoutes: enableTypedRoutes,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !isRspack &&\n        !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        new CssChunkingPlugin(config.experimental.cssChunking === 'strict'),\n      telemetryPlugin,\n      !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    typedRoutes: config.experimental.typedRoutes,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createRSCAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n"], "names": ["NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "attachReactRefresh", "babelIncludeRegexes", "getBaseWebpackConfig", "hasExternalOtelApiPackage", "loadProjectInfo", "nextImageLoaderRegex", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "React", "version", "Error", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "execOnce", "devtool", "console", "warn", "yellow", "bold", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "getReactRefreshLoader", "NEXT_RSPACK", "getRspackReactRefresh", "loader", "resolve", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "alias", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "loadJsConfig", "supportedBrowsers", "getSupportedBrowsers", "UNSAFE_CACHE_REGEX", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "edgePreviewProps", "isCompileMode", "webpack5Config", "bundler", "getWebpackBundler", "isClient", "COMPILER_NAMES", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "FlightClientEntryPlugin", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "RspackFlightClientEntryPlugin", "NextFlightClientEntryPlugin", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "enableTypedRoutes", "experimental", "typedRoutes", "bundledReactChannel", "needsExperimentalReact", "babelConfigFile", "getBabelConfigFile", "hasCustomExportOutput", "distDir", "path", "join", "useSWCLoader", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "Log", "info", "relative", "loadBindings", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "test", "or", "include", "exclude", "excludePath", "shouldBeBundled", "isResourceInPackages", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "getReactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "WEBPACK_LAYERS", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "SERVER_DIRECTORY", "reactServerCondition", "edgeConditionNames", "reactRefreshEntry", "entry", "clientEntries", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "NEXT_PROJECT_ROOT_DIST_CLIENT", "replace", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "resolveConfig", "extensionAlias", "createWebpackAliases", "getMainField", "plugins", "OptionalPeerDependencyResolverPlugin", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "Set", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "map", "transpilePackagesRegex", "handleExternals", "makeExternalHandler", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "crypto", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "isWebpackDefaultLayer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "minimize", "serverMinification", "minimizer", "getRspackCore", "SwcJsMinimizerRspackPlugin", "minimizerOptions", "compress", "inline", "global_defs", "mangle", "reserved", "disableCharFreq", "LightningCssMinimizerRspackPlugin", "targets", "MinifyPlugin", "apply", "CssMinimizerPlugin", "postcssOptions", "annotation", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "createServerOnlyClientOnlyAliases", "not", "message", "shared", "resourceQuery", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "isWebpackBundledLayer", "createNextApiEsmAliases", "isWebpackServerOnlyLayer", "createAppRouterApiAliases", "isWebpackClientOnlyLayer", "and", "metadata", "metadataImageMeta", "createRSCAliases", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "regexLikeCss", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NEXT_PROJECT_ROOT", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "MemoryWithGcCachePlugin", "maxGenerations", "injectLoader", "injectEntry", "overlay", "ReactRefreshWebpackPlugin", "webpack", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "getDefineEnvPlugin", "isTurbopack", "omitNonDeterministic", "ReactLoadablePlugin", "REACT_LOADABLE_MANIFEST", "runtimeAsset", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "DropClientPage", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "PagesManifestPlugin", "isEdgeRuntime", "MiddlewarePlugin", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "BuildManifestPlugin", "RspackProfilingPlugin", "Profiling<PERSON><PERSON><PERSON>", "WellKnownErrorsPlugin", "CopyFilePlugin", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "minimized", "AppBuildManifestPlugin", "ClientReferenceManifestPlugin", "experimentalInlineCss", "inlineCss", "NextTypesPlugin", "cacheLifeConfig", "cacheLife", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "cssChunking", "CssChunkingPlugin", "isImplicit", "baseUrl", "unshift", "JsConfigPathsPlugin", "edgeAsset", "experiments", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "cache", "maxMemoryGenerations", "Infinity", "cacheDirectory", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "buildConfiguration", "rootDirectory", "customAppFile", "escapeStringRegexp", "isDevelopment", "targetWeb", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "finalizeEntrypoint", "value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAmPaA,6BAA6B;eAA7BA;;IAbAC,yBAAyB;eAAzBA;;IAKAC,wBAAwB;eAAxBA;;IAzBAC,oBAAoB;eAApBA;;IA9BGC,kBAAkB;eAAlBA;;IAlEHC,mBAAmB;eAAnBA;;IA+Kb,OAw1EC;eAx1E6BC;;IAXdC,yBAAyB;eAAzBA;;IA3BMC,eAAe;eAAfA;;IAHTC,oBAAoB;eAApBA;;;8DAxPK;kFACoB;4BACT;+DACV;yBACK;6DACP;8BAEkB;2BACsB;uBAOlD;4BAaA;wBAEkB;yBAEU;6DACd;wBACc;0EAI5B;4EACyB;qCACI;0CACL;4EACC;iCACA;qCACI;uCACE;qBACT;gCACE;sCACe;yCACyB;+CACzB;iCACd;qEAUzB;qBACsB;wCACU;4CACI;wCACJ;yCAEC;oCACL;wCACI;iCACJ;iCAEuB;yBAInD;qDAC8C;uCAO9C;wBAC+B;mCACJ;sCAI3B;8BAIA;2BAC8C;uCACf;0EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAASC,cAAK,CAACC,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEO,MAAMX,sBAAgC;IAC3C;IACA;IACA;IACA;CACD;AAED,MAAMY,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,OAAwB;IAC3C,OACE,0BAA0B;IAC1BA,QAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuBC,IAAAA,gBAAQ,EACnC,CAACC;IACCC,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EAAC,CAAC,8BAA8B,EAAEJ,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIK,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEF,SAASC;IACP,OAAOzB,QAAQC,GAAG,CAACyB,WAAW,GAC1BC,IAAAA,gCAAqB,IAAGC,MAAM,GAC9BtC,QAAQuC,OAAO,CAACL;AACtB;AAEO,SAASzC,mBACd+C,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBP;KAC3BK,wBAAAA,cAAcjB,MAAM,sBAApBiB,8BAAAA,sBAAsBG,KAAK,qBAA3BH,4BAA6BI,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASL,cAAc;gBACzBI,KAAKE,GAAG,GAAG;oBAACL;oBAAoBI;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMV,iBACvB,kCAAkC;YAClC,CAACK,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMT,sBAAsBS,MAAMjB,yBAE3C;gBACA,MAAMkB,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMV;gBACxC,iCAAiC;gBACjCI,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGV;YAC1B;QACF;IACF;AACF;AAEO,MAAMlD,uBAAuB;IAClC+D,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB;AAEO,MAAMjF,4BAA4B;IACvC,GAAGE,oBAAoB;IACvBgF,OAAO;AACT;AAEO,MAAMjF,2BAA2B;IACtC,GAAGC,oBAAoB;IACvBgF,OAAO;IACPjB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB;AAEO,MAAM/E,gCAAgC;IAC3C,GAAGE,wBAAwB;IAC3BiF,OAAO;AACT;AAEO,MAAM1E,uBACX;AAEK,eAAeD,gBAAgB,EACpC4E,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EACpEN,KACAC;IAEF,MAAMM,oBAAoB,MAAMC,IAAAA,2BAAoB,EAACR,KAAKE;IAC1D,OAAO;QACLC;QACAC;QACAC;QACAE;IACF;AACF;AAEO,SAASpF;IACd,IAAI;QACFI,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMkF,qBAAqB;AAEZ,eAAevF,qBAC5B8E,GAAW,EACX,EACEU,OAAO,EACPC,aAAa,EACbV,MAAM,EACNW,YAAY,EACZV,MAAM,KAAK,EACXW,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACVpB,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfE,iBAAiB,EACjBiB,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,aAAa,EAiCd;QAkHC1B,sBAOIA,uBA2biBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBA4TfA,sBA+vBoBA,0BA+DtBA,2BAqCJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjCpC,gCAAAA,wBAmG0BkC,uBAuBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCX2B,yBAgLc7D,uBAmDZA,wBA0FAA,6BAAAA;IA5rEF,MAAM8D,UAAUC,IAAAA,0BAAiB;IACjC,MAAMC,WAAWnB,iBAAiBoB,0BAAc,CAACC,MAAM;IACvD,MAAMC,eAAetB,iBAAiBoB,0BAAc,CAACG,UAAU;IAC/D,MAAMC,eAAexB,iBAAiBoB,0BAAc,CAACK,MAAM;IAE3D,MAAMC,WAAWC,QAAQtG,QAAQC,GAAG,CAACyB,WAAW;IAEhD,MAAM6E,0BACJF,YAAYrG,QAAQC,GAAG,CAACuG,kCAAkC,GACtDC,4DAA6B,GAC7BC,gDAA2B;IAEjC,uFAAuF;IACvF,MAAMC,0BAA0BR,gBAAgBF;IAEhD,MAAMW,cACJ5B,SAAS6B,WAAW,CAACC,MAAM,GAAG,KAC9B9B,SAAS+B,UAAU,CAACD,MAAM,GAAG,KAC7B9B,SAASjC,QAAQ,CAAC+D,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAAC5B;IACpB,MAAM6B,0BAA0B;IAChC,MAAMC,oBAAoB,CAAC,CAAClD,OAAOmD,YAAY,CAACC,WAAW,IAAIJ;IAC/D,MAAMK,sBAAsBC,IAAAA,8CAAsB,EAACtD,UAC/C,kBACA;IAEJ,MAAMuD,kBAAkBC,IAAAA,sCAAkB,EAACzD;IAE3C,IAAI,CAACE,OAAOwD,IAAAA,6BAAqB,EAACzD,SAAS;QACzCA,OAAO0D,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUC,aAAI,CAACC,IAAI,CAAC7D,KAAKC,OAAO0D,OAAO;IAE7C,IAAIG,eAAe,CAACN,mBAAmBvD,OAAOmD,YAAY,CAACW,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIH,cAAc;YAEKvI,4BAAAA,6BAAAA;QADrB,0CAA0C;QAC1C,MAAM2I,gBAAe3I,WAAAA,QAAQ,8BAARA,8BAAAA,SAAkB4I,iBAAiB,sBAAnC5I,6BAAAA,iCAAAA,8BAAAA,2BACjB6I,MAAM;QACVJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAAC1G,qBAAqB,CAACuG,gBAAgBN,iBAAiB;QAC1Da,KAAIC,IAAI,CACN,CAAC,6EAA6E,EAAEV,aAAI,CAACW,QAAQ,CAC3FvE,KACAwD,iBACA,+CAA+C,CAAC;QAEpDjG,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAACiG,mBAAmBzB,UAAU;QAChC,MAAMyC,IAAAA,iBAAY,EAACvE,OAAOmD,YAAY,CAACqB,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvCzE,CAAAA,OAAO0E,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAACpJ;IAET,KAAK,MAAMqJ,OAAO5E,OAAOmD,YAAY,CAAC0B,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAACrH,gCAAgC,CAACsG,gBAAgB7D,OAAOgF,QAAQ,EAAE;QACrEZ,KAAIC,IAAI,CACN;QAEF9G,+BAA+B;IACjC;IAEA,MAAM0H,4BACJjF,OAAOmD,YAAY,CAAC+B,WAAW,IAAI,CAAC,CAAClF,OAAO0E,iBAAiB;IAC/D,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIJ,4BAEA,CAAC,IACD;YAAEK,SAAS;gBAACvF;mBAAQ/E;aAAoB;QAAC,CAAC;QAC9CuK,SAAS,CAACC;YACR,IAAIxK,oBAAoBwD,IAAI,CAAC,CAACC,IAAMA,EAAE2G,IAAI,CAACI,eAAe;gBACxD,OAAO;YACT;YAEA,MAAMC,kBAAkBC,IAAAA,qCAAoB,EAC1CF,aACAf;YAEF,IAAIgB,iBAAiB,OAAO;YAE5B,OAAOD,YAAYV,QAAQ,CAAC;QAC9B;IACF;IAEA,MAAMa,cAAcC,IAAAA,oCAAc,EAChC/B,cACAN,iBACAZ,yBACAe,SACA5C,UACAf,KACCqB,UAAUN,UACXb,KACA6B,WACA9B,uBAAAA,OAAOmD,YAAY,qBAAnBnD,qBAAqB6F,aAAa,EAClCV,cAAcI,OAAO;IAGvB,MAAMO,sBAAsBH,cACxB3B,YACA+B,IAAAA,4CAAsB,GACpB/F,wBAAAA,OAAOmD,YAAY,qBAAnBnD,sBAAqB6F,aAAa,EAClC9F,KACAE,KACA0C,yBACAwC,cAAcI,OAAO;IAG3B,IAAIS,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBlG;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQmD,YAAY,qBAApBnD,qBAAsBmG,iBAAiB,KACvC,CAACH,8BACD;gBAMA1K,oCAAAA;YALA,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvD0K,+BAA+B;aAC/B1K,WAAAA,QAAQ,8BAARA,qCAAAA,SAAkB8K,yBAAyB,qBAA3C9K,wCAAAA,UACEqI,aAAI,CAACC,IAAI,CAACF,SAAS,CAAC,kBAAkB,EAAE2C,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsBvK,QAAQC,GAAG,CAACuK,kBAAkB;QAC1D,IAAInE,YAAYkE,qBAAqB;gBAwB7BrG,2BAGAA;YA1BN,OAAO;gBACLtC,QAAQ;gBACR6I,SAAS;oBACPC,UAAU/D;oBACVgE,SAAS5G;oBACTe;oBACAM;oBACAwF,iBAAiB3G,OAAO6B;oBACxB4C,mBAAmBD;oBACnBnE;oBACAuG,aAAalD,aAAI,CAACC,IAAI,CACpB7D,KACAC,CAAAA,0BAAAA,OAAQ0D,OAAO,KAAI,SACnB,SACA;oBAEFoD,yBAAyBpG;oBAEzB,0BAA0B;oBAC1BqG,KAAKzE,QAAQtG,QAAQgL,QAAQ,CAACD,GAAG;oBACjCE,qBAAqB3E,QAAQtC,OAAOmD,YAAY,CAAC8D,mBAAmB;oBACpEC,mBAAmBlH,OAAOkH,iBAAiB;oBAC3CC,YAAY7E,QACVpC,6BAAAA,4BAAAA,SAAUkH,eAAe,qBAAzBlH,0BAA2BmH,sBAAsB;oBAEnDC,uBAAuBhF,QACrBpC,6BAAAA,6BAAAA,SAAUkH,eAAe,qBAAzBlH,2BAA2BoH,qBAAqB;oBAElDC,wBAAwBjM,QAAQuC,OAAO,CACrC;oBAGF,GAAGqI,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACLtI,QAAQ;YACR6I,SAAS;gBACPC,UAAU/D;gBACVgE,SAAS5G;gBACTe;gBACAM;gBACAwF,iBAAiB3G,OAAO6B;gBACxB0F,YAAYxH;gBACZE;gBACAwE,mBAAmBD;gBACnBnE;gBACAuG,aAAalD,aAAI,CAACC,IAAI,CAAC7D,KAAKC,CAAAA,0BAAAA,OAAQ0D,OAAO,KAAI,SAAS,SAAS;gBACjEoD,yBAAyBpG;gBACzB,GAAGwF,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMuB,uBAAuBxB,aAAa;QACxCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACC,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB9B,aAAa;QACrCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACI,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwBhC,aAAa;QACzCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACM,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBlC,aAAa;QACpCyB,kBAAkB;QAClBI,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAOxE,eAAesE,mBAAmBxC;IAC3C;IAEA,MAAM2C,wBAAwBtF,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/CyE;QACA9B;QACAG;KACD,CAACzJ,MAAM,CAACiG,WACT,EAAE;IAEN,MAAMiG,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cd;QACA9B;KACD,CAACtJ,MAAM,CAACiG;IAET,MAAMkG,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CvC,aAAa;YACXyB,kBAAkB;YAClBC,aAAaC,yBAAc,CAACa,UAAU;QACxC;QACA9C;KACD,CAACtJ,MAAM,CAACiG;IAET,MAAMoG,sBAAsBzI,OAAO6B,WAAW;QAACrE;KAAwB,GAAG,EAAE;IAE5E,2CAA2C;IAC3C,MAAMkL,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvB9K,QAAQ;YACV;eACIoF,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/C4F,iBAAiBX,wBAAwBF;gBACzCpC;gBACAG;aACD,CAACzJ,MAAM,CAACiG,WACT,EAAE;SACP;IAED,MAAMwG,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwBnF,eAC1BoC,aAAa;QACXyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACqB,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiBlJ,OAAOkJ,cAAc;IAE5C,MAAMC,aAAaxG,0BACfgB,aAAI,CAACC,IAAI,CAACF,SAAS0F,4BAAgB,IACnC1F;IAEJ,MAAM2F,uBAAuB;QAC3B;WACIpH,eAAeqH,2BAAkB,GAAG,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMC,oBAAoBlH,WACtB1E,IAAAA,gCAAqB,IAAG6L,KAAK,GAC7BlO,QAAQuC,OAAO,CACb,CAAC,yDAAyD,CAAC;IAGjE,MAAM4L,gBAAgB3H,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAI7B,MACA;YACE,CAACyJ,qDAAyC,CAAC,EAAEH;YAC7C,CAACI,2CAA+B,CAAC,EAC/B,CAAC,EAAE,CAAC,GACJhG,aAAI,CACDW,QAAQ,CACPvE,KACA4D,aAAI,CAACC,IAAI,CAACgG,2CAA6B,EAAE,OAAO,YAEjDC,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACC,4CAAgC,CAAC,EAChC,CAAC,EAAE,CAAC,GACJnG,aAAI,CACDW,QAAQ,CACPvE,KACA4D,aAAI,CAACC,IAAI,CACPgG,2CAA6B,EAC7B3J,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzB4J,OAAO,CAAC,OAAO;QACpB,GAAI7G,YACA;YACE,CAAC+G,gDAAoC,CAAC,EAAE9J,MACpC;gBACEsJ;gBACA,CAAC,EAAE,CAAC,GACF5F,aAAI,CACDW,QAAQ,CACPvE,KACA4D,aAAI,CAACC,IAAI,CACPgG,2CAA6B,EAC7B,oBAGHC,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFlG,aAAI,CACDW,QAAQ,CACPvE,KACA4D,aAAI,CAACC,IAAI,CACPgG,2CAA6B,EAC7B,gBAGHC,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACA7F;IAEJ,MAAMgG,gBAAkD;QACtD,yCAAyC;QACzC5K,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpE6K,gBAAgBjK,OAAOmD,YAAY,CAAC8G,cAAc;QAClDnL,SAAS;YACP;eACG/C;SACJ;QACD+D,OAAOoK,IAAAA,2CAAoB,EAAC;YAC1BxG;YACA5B;YACAG;YACAE;YACAlC;YACAD;YACAc;YACAM;YACArB;YACAgB;YACA6B;QACF;QACA,GAAId,WACA;YACE/C,UAAU;gBACR/C,SAASV,QAAQuC,OAAO,CAAC;YAC3B;QACF,IACAmG,SAAS;QACb,oFAAoF;QACpFzE,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;QACvC,GAAIsB,gBAAgB;YAClB/C,gBAAgBoK,2BAAkB;QACpC,CAAC;QACDc,SAAS;YACPjI,eAAe,IAAIkI,yEAAoC,KAAKrG;SAC7D,CAAC3H,MAAM,CAACiG;QACT,GAAKD,YAAYlC,eACb;YACEmK,UAAU;gBACRC,YAAYpK;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAMqK,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAIC;IACrC,iDAAiD;IACjD,MAAMC,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIL,yBAAyBM,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAH,yBAAyBO,GAAG,CAACJ;YAE7B,MAAMK,kBAAkB5P,QAAQuC,OAAO,CAAC,GAAGgN,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAYxH,aAAI,CAACC,IAAI,CAACsH,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMjG,QAAQ,CAACqG,YAAY;YAC/BJ,MAAMhG,IAAI,CAACoG;YACX,MAAMC,eAAe9P,QAAQ4P,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQ7O,OAAO8O,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACI7H,YACA;YACE,CAAC,wBAAwB,EAAEK,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDuH,eAAeC,aAAa9K,KAAK0K;IACnC;IACAG,eAAe,QAAQ7K,KAAKyK;IAE5B,MAAMgB,cAAcxL,OAAOwL,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAIxL,OAAOyL,sBAAsB,IAAIhH,wBAAwB;QAC3D,MAAMiH,2BAA2BjH,uBAAuBpI,MAAM,CAAC,CAACuI;gBAC9D5E;oBAAAA,iCAAAA,OAAOyL,sBAAsB,qBAA7BzL,+BAA+B8E,QAAQ,CAACF;;QAE1C,IAAI8G,yBAAyB5I,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAInH,MACR,CAAC,8FAA8F,EAAE+P,yBAAyB9H,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAM+H,yBAAyBtQ,kBAAkBsJ,MAAM,IACjD3E,OAAOyL,sBAAsB,IAAI,EAAE,EACvCpP,MAAM,CAAC,CAACuI,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMgH,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BG,GAAG,CAAC,CAACxP,IAAMA,EAAEuN,OAAO,CAAC,OAAO,YAC5BjG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMmI,yBAAyB,IAAIF,OACjC,CAAC,2BAA2B,EAAEpH,0CAAAA,uBAC1BqH,GAAG,CAAC,CAACxP,IAAMA,EAAEuN,OAAO,CAAC,OAAO,YAC7BjG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMoI,kBAAkBC,IAAAA,oCAAmB,EAAC;QAC1CjM;QACA4L;QACAM,oBAAoBzH;QACpB1E;IACF;IAEA,MAAMoM,sBAAsB,IAAIN,OAAO,CAAC,IAAI,EAAE3C,eAAetF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMwI,yBAAyB;QAACjH,cAAcC,IAAI;QAAE+G;KAAoB;IAExE,MAAME,iBAAiB/Q,QAAQ,UAAU+Q,cAAc;IAEvD,MAAMC,kCACJ,CAAC,CAACtM,OAAOmD,YAAY,CAACoJ,mBAAmB,IAAItM;IAE/C,MAAMuM,iBAAiB;QACrB,MAAMC,WAAWC,OAAO1Q,QAAQC,GAAG,CAAC0Q,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZvP,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAOsP,YAAYzI;IACrB;IAEA,MAAM4I,kBACJ,CAAC3M,OACD6B,YACA,IAAI,AACFxG,CAAAA,QAAQ,sDAAqD,EAC7DuR,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAajJ;SAAa;QAC3B;YAAC;YAAY,CAAC,GAAC7D,mBAAAA,OAAOgF,QAAQ,qBAAfhF,iBAAiB+M,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAAC/M,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBgN,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAAChN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBiN,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAAC/M,6BAAAA,4BAAAA,SAAUkH,eAAe,qBAAzBlH,0BAA2BmH,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAACrH,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBkN,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAAChN,6BAAAA,6BAAAA,SAAUkH,eAAe,qBAAzBlH,2BAA2BiN,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAACnN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBoN,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAACpN,OAAO0E,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAAC1E,OAAOqN,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAACrN,OAAOsN,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAACtN,OAAOkH,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgBlH,OAAOmD,YAAY,CAACoK,YAAY,KAAK;SAAK;QAC3DxJ;KACD,CAAC1H,MAAM,CAAqBiG;IAInC,IAAIxE,gBAAuC;QACzC0P,aAAahB;QACb,GAAIrK,eAAe;YAAEsL,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACE7L,YAAYG,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACA2L,IAAAA,0CAAwB;gBACxBC,qDAAmC;aACpC,GACD,EAAE;SACP,GACD;eACKxB;YACH,CAAC,EACCyB,OAAO,EACPC,OAAO,EACPlP,cAAc,EACdmP,WAAW,EACXC,UAAU,EAqBX,GACCjC,gBACE8B,SACAC,SACAlP,gBACAmP,YAAYE,WAAW,EACvB,CAACzH;oBACC,MAAM0H,kBAAkBF,WAAWxH;oBACnC,OAAO,CAAC2H,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAACzQ,SAAS0Q;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAO5Q,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAM8Q,QAAQ,SAASvJ,IAAI,CAACqJ,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkC5R,IAAI,MACtC,WACA,UAAUsI,IAAI,CAACqJ;gCACnB5Q,QAAQ;oCAAC4Q;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAAC7O;YACf8O,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAIhP,KAAK;oBACP,IAAIkC,cAAc;wBAChB;;;;;YAKA,GACA,MAAM+M,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBrK,MAAM;oCACNsK,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBxE,MAAM,CAACxO;wCACL,MAAMiT,WAAWjT,QAAOkT,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACN;4CAC9CG,KAAKG,MAAM,CAACN;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKI,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIpO,gBAAgBF,cAAc;oBAChC,OAAO;wBACLuO,UAAU,GAAGvO,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1DuN,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMc,sBAAsB;oBAC1BjB,QAAQ;oBACRnE,MAAM;oBACN,6DAA6D;oBAC7DqF,OAAOC,4BAAqB;oBAC5BvL,MAAKvI,OAAW;wBACd,MAAM+T,WAAW/T,QAAOkT,gBAAgB,oBAAvBlT,QAAOkT,gBAAgB,MAAvBlT;wBACjB,OAAO+T,WACHnG,uBAAuBjM,IAAI,CAAC,CAACqS,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpB7L,MAAKvI,OAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,QAAOC,IAAI,qBAAXD,aAAaiU,UAAU,CAAC,WACzBjU,QAAOqU,IAAI,KAAK,UAChB,oBAAoB9L,IAAI,CAACvI,QAAOkT,gBAAgB,MAAM;oBAE1D;oBACA1E,MAAKxO,OAKJ;wBACC,MAAMoT,OAAOC,eAAM,CAACC,UAAU,CAAC;wBAC/B,IAAIvT,YAAYC,UAAS;4BACvBA,QAAOsU,UAAU,CAAClB;wBACpB,OAAO;4BACL,IAAI,CAACpT,QAAOuU,QAAQ,EAAE;gCACpB,MAAM,qBAEL,CAFK,IAAIzV,MACR,CAAC,iCAAiC,EAAEkB,QAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;2CAAA;gDAAA;kDAAA;gCAEN;4BACF;4BACAmT,KAAKG,MAAM,CAACvT,QAAOuU,QAAQ,CAAC;gCAAEtD,SAAS/N;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAIlD,QAAO6T,KAAK,EAAE;4BAChBT,KAAKG,MAAM,CAACvT,QAAO6T,KAAK;wBAC1B;wBAEA,OAAOT,KAAKI,MAAM,CAAC,OAAOgB,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVpB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQnN,WAEJ,YAAY;oBACZ,mCACA,CAACiP,QACC,CAAC,iCAAiClM,IAAI,CAACkM,MAAMjG,IAAI;oBAEvD,mDAAmD;oBACnDiE,aAAajN,WACT,CAAC,IACD;wBACEkP,WAAWd;wBACXe,KAAKP;oBACP;oBACJpB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA+B,cAAc3P,WACV;gBAAEuJ,MAAMqG,+CAAmC;YAAC,IAC5C1N;YAEJ2N,UACE,CAAC1R,OACA6B,CAAAA,YACCG,gBACCE,gBAAgBnC,OAAOmD,YAAY,CAACyO,kBAAkB;YAC3DC,WAAWxP,WACP;gBACE,IAAKyP,CAAAA,IAAAA,wBAAa,GAAC,EAAEC,0BAA0B,CAAE;oBAC/C,6BAA6B;oBAC7B,iEAAiE;oBACjEC,kBAAkB;wBAChBC,UAAU;4BACRC,QAAQ;4BACRC,aAAa;gCACX,mDAAmD;4BACrD;wBACF;wBACAC,QAAQ,CAAC9Q,cAAc;4BACrB+Q,UAAU;gCAAC;6BAAc;4BACzBC,iBAAiB,CAACxQ;wBACpB;oBACF;gBACF;gBACA,IAAKgQ,CAAAA,IAAAA,wBAAa,GAAC,EAAES,iCAAiC,CAAE;oBACtD,8BAA8B;oBAC9BP,kBAAkB;wBAChBQ,SAASlS;oBACX;gBACF;aACD,GACD;gBACE,oBAAoB;gBACpB,CAAC0E;oBACC,4BAA4B;oBAC5B,MAAM,EAAEyN,YAAY,EAAE,GACpBnX,QAAQ;oBACV,IAAImX,aAAa;wBAAEnR;oBAAW,GAAGoR,KAAK,CAAC1N;gBACzC;gBACA,aAAa;gBACb,CAACA;oBACC,MAAM,EACJ2N,kBAAkB,EACnB,GAAGrX,QAAQ;oBACZ,IAAIqX,mBAAmB;wBACrBC,gBAAgB;4BACd9G,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/CoG,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5DW,YAAY;4BACd;wBACF;oBACF,GAAGH,KAAK,CAAC1N;gBACX;aACD;QACP;QACA8I,SAAS/N;QACT,8CAA8C;QAC9CyJ,OAAO;YACL,OAAO;gBACL,GAAIC,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG7I,WAAW;YAChB;QACF;QACAkS,cAActW,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnBwW,IAAI,GAAE/S,uBAAAA,OAAO8S,YAAY,qBAAnB9S,qBAAqBgT,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACVlT,OAAOmT,WAAW,GACdnT,OAAOmT,WAAW,CAACC,QAAQ,CAAC,OAC1BpT,OAAOmT,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7BrT,OAAOmT,WAAW,GACpB,GACL,OAAO,CAAC;YACTxP,MAAM,CAAC1D,OAAOkC,eAAewB,aAAI,CAACC,IAAI,CAACuF,YAAY,YAAYA;YAC/D,oCAAoC;YACpCqH,UAAU7N,0BACN1C,OAAOgC,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAEpB,gBAAgB,cAAc,GAAG,MAAM,EACtDZ,MAAM,KAAKmB,SAAS,iBAAiB,iBACtC,GAAG,CAAC;YACTkS,SAASxR,YAAYG,eAAe,SAAS+B;YAC7CuP,eAAezR,YAAYG,eAAe,WAAW;YACrDuR,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAe/Q,0BACX,cACA,CAAC,cAAc,EAAE9B,gBAAgB,cAAc,KAC7CZ,MAAM,WAAW,uBAClB,GAAG,CAAC;YACT0T,+BAA+B;YAC/BC,oBAAoBpI;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3FqI,+BAA+B5T,MAC3B,6BACA+D;YACJ8P,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACbpW,SAASmM;QACTkK,eAAe;YACb,+BAA+B;YAC/BpU,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAACqU,MAAM,CACN,CAACrU,OAAOlC;gBACN,4DAA4D;gBAC5DkC,KAAK,CAAClC,OAAO,GAAG+F,aAAI,CAACC,IAAI,CAACwQ,WAAW,WAAW,WAAWxW;gBAE3D,OAAOkC;YACT,GACA,CAAC;YAEHhB,SAAS;gBACP;mBACG/C;aACJ;YACDqO,SAAS,EAAE;QACb;QACAvN,QAAQ;YACNoB,OAAO;gBACL,+EAA+E;gBAC/E;oBACEiQ,aAAa;wBACX7I,IAAI;+BACCuC,yBAAc,CAACyM,KAAK,CAACC,UAAU;+BAC/B1M,yBAAc,CAACyM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA1W,SAAS;wBACP,6CAA6C;wBAC7CiC,OAAO0U,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA;oBACEtG,aAAa;wBACXuG,KAAK;+BACA7M,yBAAc,CAACyM,KAAK,CAACC,UAAU;+BAC/B1M,yBAAc,CAACyM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA1W,SAAS;wBACP,6CAA6C;wBAC7CiC,OAAO0U,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACEpP,MAAM;wBACJ;wBACA;qBACD;oBACDxH,QAAQ;oBACRsQ,aAAa;wBACX7I,IAAIuC,yBAAc,CAACyM,KAAK,CAACC,UAAU;oBACrC;oBACA7N,SAAS;wBACPiO,SACE;oBACJ;gBACF;gBACA;oBACEtP,MAAM;wBACJ;wBACA;qBACD;oBACDxH,QAAQ;oBACRsQ,aAAa;wBACXuG,KAAK;+BACA7M,yBAAc,CAACyM,KAAK,CAACC,UAAU;+BAC/B1M,yBAAc,CAACyM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA9N,SAAS;wBACPiO,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACEtP,MAAM;wBACJ;wBACA;qBACD;oBACDxH,QAAQ;oBACRsQ,aAAa;wBACX7I,IAAIuC,yBAAc,CAACyM,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACIpS,eACA,EAAE,GACF;oBACE;wBACEiD,MAAM;wBACNxH,QAAQ;oBACV;iBACD;mBACDoF,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACV0N,OAAO9I,yBAAc,CAAC+M,MAAM;wBAC5BvP,MAAMtJ;oBACR;oBACA,4CAA4C;oBAC5C;wBACE8Y,eAAe,IAAI/I,OACjBgJ,mCAAwB,CAACC,aAAa;wBAExCpE,OAAO9I,yBAAc,CAACC,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C6I,OAAO9I,yBAAc,CAACI,mBAAmB;wBACzC5C,MAAM;oBACR;oBACA;wBACE8I,aAAa6G,4BAAqB;wBAClClX,SAAS;4BACPiC,OAAOkV,IAAAA,8CAAuB;wBAChC;oBACF;oBACA;wBACE9G,aAAa+G,+BAAwB;wBACrCpX,SAAS;4BACPiC,OAAOoV,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;oBACA;wBACEhH,aAAaiH,+BAAwB;wBACrCtX,SAAS;4BACPiC,OAAOoV,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;iBACD,GACD,EAAE;mBACFlS,aAAa,CAAClB,WACd;oBACE;wBACEoM,aAAa+G,+BAAwB;wBACrC7P,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzBgQ,KAAK;gCACHhJ;gCACA;oCACEqI,KAAK;wCAAC7I;wCAA4B9P;qCAAmB;gCACvD;6BACD;wBACH;wBACA8Y,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAI5I,OAAOgJ,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAIxJ,OAAOgJ,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACAzX,SAAS;4BACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;4BACvCzB,gBAAgBmK;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9BvJ,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;gCAC3C,iCAAiC;gCACjCtC;gCACA2P,OAAO9I,yBAAc,CAACC,qBAAqB;gCAC3C5F;4BACF;wBACF;wBACA5D,KAAK;oBACP;iBACD,GACD,EAAE;gBACN,kDAAkD;gBAClD,yDAAyD;mBACrD,CAAC2B,OAAOmD,YAAY,CAACzD,cAAc,GACnC;oBACE;wBACE0F,MAAM;wBACNvH,SAAS;4BACP6B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACFsD,aAAaf,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE2S,eAAe,IAAI/I,OACjBgJ,mCAAwB,CAACW,YAAY;wBAEvC9E,OAAO9I,yBAAc,CAACC,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACF7E,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEyS,OAAO;4BACL;gCACEvH,aAAa+G,+BAAwB;gCACrC7P,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzBgQ,KAAK;wCACHhJ;wCACA;4CACEqI,KAAK;gDAAC7I;gDAA4B9P;6CAAmB;wCACvD;qCACD;gCACH;gCACA+B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5DiC,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;wCAC3CtC;wCACA2P,OAAO9I,yBAAc,CAACC,qBAAqB;wCAC3C5F;oCACF;gCACF;4BACF;4BACA;gCACEmD,MAAMgH;gCACN8B,aAAatG,yBAAc,CAACI,mBAAmB;gCAC/CnK,SAAS;oCACPiC,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;wCAC3CtC;wCACA2P,OAAO9I,yBAAc,CAACI,mBAAmB;wCACzC/F;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACEmD,MAAMgH;wBACN8B,aAAatG,yBAAc,CAACM,eAAe;wBAC3CrK,SAAS;4BACPiC,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;gCAC3CtC;gCACA2P,OAAO9I,yBAAc,CAACM,eAAe;gCACrCjG;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7Ee,aAAa/C,OAAO6B,WACpB;oBACE;wBACEsD,MAAMD,cAAcC,IAAI;wBACxBG,SAAS;4BACP,+CAA+C;4BAC/CJ,cAAcI,OAAO;4BACrBwG;4BACAlQ;yBACD;wBACDqS,aAAatG,yBAAc,CAACM,eAAe;wBAC3C7J,KAAKqK;wBACL7K,SAAS;4BACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACE8U,OAAO;wBACL;4BACE,GAAGtQ,aAAa;4BAChB+I,aAAatG,yBAAc,CAACqB,OAAO;4BACnC5K,KAAK2K;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9D0M,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAGxQ,aAAa;4BAChB+I,aAAatG,yBAAc,CAACgO,OAAO;4BACnCvX,KAAK2K;wBAGP;wBACA;4BACE5D,MAAMD,cAAcC,IAAI;4BACxB8I,aAAatG,yBAAc,CAACa,UAAU;4BACtCpK,KAAKmK;4BACL3K,SAAS;gCACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;gCACvCzB,gBAAgBmK;gCAChBvJ,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;oCAC3CtC;oCACA2P,OAAO9I,yBAAc,CAACa,UAAU;oCAChCxG;gCACF;4BACF;wBACF;wBACA;4BACEmD,MAAMD,cAAcC,IAAI;4BACxB8I,aAAatG,yBAAc,CAACiO,UAAU;4BACtCxX,KAAKkK;4BACL1K,SAAS;gCACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;gCACvCzB,gBAAgBmK;gCAChBvJ,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;oCAC3CtC;oCACA2P,OAAO9I,yBAAc,CAACiO,UAAU;oCAChC5T;gCACF;4BACF;wBACF;2BACIe,YACA;4BACE;gCACEoC,MAAMD,cAAcC,IAAI;gCACxB8I,aAAa+G,+BAAwB;gCACrC1P,SAASzJ;gCACTuC,KAAKiK;4BACP;4BACA;gCACElD,MAAMD,cAAcC,IAAI;gCACxBwP,eAAe,IAAI/I,OACjBgJ,mCAAwB,CAACW,YAAY;gCAEvCnX,KAAKiK;4BACP;4BACA;gCACElD,MAAMD,cAAcC,IAAI;gCACxB8I,aAAatG,yBAAc,CAACM,eAAe;gCAC3C,uEAAuE;gCACvE3C,SAAS3J;gCACTyC,KAAKyK;gCACLjL,SAAS;oCACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;gCACzC;4BACF;4BACA;gCACEyE,MAAMD,cAAcC,IAAI;gCACxB8I,aAAatG,yBAAc,CAACI,mBAAmB;gCAC/CzC,SAASzJ;gCACTuC,KAAK0K;gCACLlL,SAAS;oCACP0B,YAAY4K,IAAAA,qBAAY,EAACxJ,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAGwE,aAAa;4BAChB9G,KAAK;mCACAqK;gCACHN,eAAeC,KAAK;gCACpBvC;6BACD,CAACzJ,MAAM,CAACiG;wBACX;qBACD;gBACH;mBAEI,CAACtC,OAAO8V,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACE3Q,MAAMhK;wBACNwC,QAAQ;wBACRoY,QAAQ;4BAAEvB,KAAKwB,iBAAY;wBAAC;wBAC5BC,YAAY;4BAAEzB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAI5I,OAAOgJ,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAIxJ,OAAOgJ,mCAAwB,CAACC,aAAa;gCACjD,IAAIjJ,OAAOgJ,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACA7O,SAAS;4BACP0P,OAAOlW;4BACPU;4BACAyV,UAAUpW,OAAOoW,QAAQ;4BACzBjD,aAAanT,OAAOmT,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFlR,eACA;oBACE;wBACEpE,SAAS;4BACPkB,UAAU;gCACR/C,SAASV,QAAQuC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACDiE,WACE;oBACE;wBACEjE,SAAS;4BACPkB,UACEiB,OAAOmD,YAAY,CAACkT,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACXtG,QAAQ;gCACRuG,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJjT,MAAM;gCACNkT,UAAU;gCACV7a,SAAS;gCACT8a,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQhb,QAAQuC,OAAO,CACrB;gCAEF0Y,QAAQjb,QAAQuC,OAAO,CACrB;gCAEF2Y,WAAWlb,QAAQuC,OAAO,CACxB;gCAEFqS,QAAQ5U,QAAQuC,OAAO,CACrB;gCAEF4Y,QAAQnb,QAAQuC,OAAO,CACrB;gCAEF6Y,MAAMpb,QAAQuC,OAAO,CACnB;gCAEF8Y,OAAOrb,QAAQuC,OAAO,CACpB;gCAEF+Y,IAAItb,QAAQuC,OAAO,CACjB;gCAEF8F,MAAMrI,QAAQuC,OAAO,CACnB;gCAEFgZ,UAAUvb,QAAQuC,OAAO,CACvB;gCAEF7B,SAASV,QAAQuC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5BiZ,aAAaxb,QAAQuC,OAAO,CAC1B;gCAEFkZ,QAAQzb,QAAQuC,OAAO,CACrB;gCAEFmZ,gBAAgB1b,QAAQuC,OAAO,CAC7B;gCAEFoZ,KAAK3b,QAAQuC,OAAO,CAAC;gCACrBqZ,QAAQ5b,QAAQuC,OAAO,CACrB;gCAEFsZ,KAAK7b,QAAQuC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/BuZ,MAAM9b,QAAQuC,OAAO,CAAC;gCACtBwZ,IAAI/b,QAAQuC,OAAO,CACjB;gCAEFyZ,MAAMhc,QAAQuC,OAAO,CACnB;gCAEF0Z,QAAQjc,QAAQuC,OAAO,CACrB;gCAEF2Z,cAAclc,QAAQuC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7BuH,MAAM;oBACNqS,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACErS,MAAM;oBACNqS,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5DrS,MAAM;oBACN/G,KAAK,CAAC,EAAEuW,aAAa,EAA6B;4BAE9CA;wBADF,MAAM8C,QAAQ,AACZ9C,CAAAA,EAAAA,uBAAAA,cAAcvF,KAAK,CAAC,uCAApBuF,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChDzY,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEyB,QAAQ;gCACR6I,SAAS;oCACPiR;oCACA7Q,aAAalD,aAAI,CAACC,IAAI,CACpB7D,KACAC,CAAAA,0BAAAA,OAAQ0D,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChBiU,OAAO,wBAAwB/C;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACE/W,SAAS;wBACPiC,OAAO;4BACL8X,MAAMC,+BAAiB;wBACzB;oBACF;gBACF;aACD;QACH;QACAzN,SAAS;YACPjI,gBACE,IAAIP,QAAQkW,6BAA6B,CACvC,6BACA,SAAUlH,QAAQ;gBAChB,MAAMmH,aAAapU,aAAI,CAACqU,QAAQ,CAC9BpH,SAAS7C,OAAO,EAChB;gBAEF,MAAM2C,QAAQE,SAAS5C,WAAW,CAACE,WAAW;gBAC9C,IAAI+J;gBAEJ,OAAQvH;oBACN,KAAK9I,yBAAc,CAACI,mBAAmB;oBACvC,KAAKJ,yBAAc,CAACC,qBAAqB;oBACzC,KAAKD,yBAAc,CAACM,eAAe;oBACnC,KAAKN,yBAAc,CAACsQ,aAAa;wBAC/BD,UAAU;wBACV;oBACF;wBACEA,UAAU;gBACd;gBACArH,SAAS7C,OAAO,GAAG,CAAC,+BAA+B,EAAEkK,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJ9X,OAAO,IAAIkY,gDAAuB,CAAC;gBAAEC,gBAAgB;YAAE;YACvDnY,OACE6B,YACCO,CAAAA,WAEG,IAAK1E,CAAAA,IAAAA,gCAAqB,GAAC,EAAU;gBACnC0a,cAAc;gBACdC,aAAa;gBACbC,SAAS;YACX,KACA,IAAIC,kCAAyB,CAACC,gBAAO,CAAA;YAC3C,6GAA6G;YAC5G3W,CAAAA,YAAYG,YAAW,KACtB,IAAIL,QAAQ8W,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAACrd,QAAQuC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAIiE,YAAY;oBAAE9F,SAAS;wBAACV,QAAQuC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YACF+a,IAAAA,mCAAkB,EAAC;gBACjBC,aAAa;gBACb7Y;gBACAC;gBACAyD;gBACAlC;gBACAoB;gBACAd;gBACAG;gBACAU;gBACAR;gBACAd;gBACAyX,sBAAsBpX;YACxB;YACAI,YACE,IAAIiX,wCAAmB,CAAC;gBACtBvI,UAAUwI,mCAAuB;gBACjClY;gBACAM;gBACA6X,cAAc,CAAC,OAAO,EAAEC,8CAAkC,CAAC,GAAG,CAAC;gBAC/DjZ;YACF;YACF,oDAAoD;YACpD,CAACoC,YAAaP,CAAAA,YAAYG,YAAW,KAAM,IAAIkX,wCAAc;YAC7DhX,gBACE,CAAClC,OACD,IAAK3E,CAAAA,QAAQ,kDAAiD,EAC3D8d,sBAAsB,CACvB;gBACEzS,SAAS5G;gBACTqB,QAAQA;gBACRN,UAAUA;gBACVyM,cAAcvN,OAAOmD,YAAY,CAACoK,YAAY;gBAC9C8L,uBAAuBrZ,OAAOqZ,qBAAqB;gBACnDC,eAAetW;gBACfuW,cAAc,EAAE;gBAChB5Y;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClEX,OAAOwZ,2BAA2B,IAChC,IAAI5X,QAAQ6X,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACE1Z,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAE2Z,6BAA6B,EAAE,GACrCte,QAAQ;gBACV,MAAMue,aAAoB;oBACxB,IAAID,8BAA8B;wBAChClS,kBAAkB1E;oBACpB;iBACD;gBAED,IAAIlB,YAAYG,cAAc;oBAC5B4X,WAAW9U,IAAI,CAAC,IAAInD,QAAQkY,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAAC5Z,OACC,IAAI2B,QAAQ6X,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACFhX,2BACE,IAAIoX,4BAAmB,CAAC;gBACtB9Z;gBACAqZ,eAAetW;gBACfgX,eAAe/X;gBACfyB,SAAS,CAACzD,MAAMyD,UAAUM;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxD/B,gBACE,IAAIgY,yBAAgB,CAAC;gBACnBha;gBACAia,YAAY,CAACja,OAAO,CAAC,GAACD,2BAAAA,OAAOmD,YAAY,CAACgX,GAAG,qBAAvBna,yBAAyBoa,SAAS;gBACxDpZ;gBACAqZ,kBAAkB;oBAChBC,iBAAiB7Z;oBACjB8Z,oCAAoC7Z;oBACpC,GAAGe,gBAAgB;gBACrB;YACF;YACFK,YACE,IAAI0Y,4BAAmB,CAAC;gBACtB/Z;gBACAO;gBACAH;gBACAyY,eAAetW;gBACfzB;YACF;YACFc,WACI,IAAIoY,4CAAqB,CAAC;gBAAEtZ;YAAe,KAC3C,IAAIuZ,gCAAe,CAAC;gBAAEvZ;gBAAgBwF,SAAS5G;YAAI;YACvD,IAAI4a,4CAAqB;YACzB7Y,YACE,IAAI8Y,8BAAc,CAAC;gBACjB,yDAAyD;gBACzDC,UAAUvf,QAAQuC,OAAO,CAAC;gBAC1Bid,UAAU9e,QAAQC,GAAG,CAAC8e,cAAc;gBACpC1P,MAAM,CAAC,uBAAuB,EAAEpL,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzD0R,UAAU;gBACVtN,MAAM;oBACJ,CAAC2W,wDAA4C,CAAC,EAAE;oBAChD,gCAAgC;oBAChCC,WAAW;gBACb;YACF;YACFjY,aAAalB,YAAY,IAAIoZ,8CAAsB,CAAC;gBAAEjb;YAAI;YAC1D+C,aACGlB,CAAAA,WACG,IAAIqZ,mDAA6B,CAAC;gBAChClb;gBACAmB;gBACAga,uBAAuB,CAAC,CAACpb,OAAOmD,YAAY,CAACkY,SAAS;YACxD,KACA,IAAI9Y,wBAAwB;gBAC1BnB;gBACAnB;gBACAgC;gBACAvB;YACF,EAAC;YACPsC,aACE,CAAClB,YACD,IAAIwZ,gCAAe,CAAC;gBAClBvb;gBACA2D,SAAS1D,OAAO0D,OAAO;gBACvBtC;gBACAnB;gBACAgC;gBACAiH,gBAAgBlJ,OAAOkJ,cAAc;gBACrC9F,aAAaF;gBACbqY,iBAAiBvb,OAAOmD,YAAY,CAACqY,SAAS;gBAC9Cva;gBACAC;YACF;YACF,CAACjB,OACC6B,YACA,CAAC,GAAC9B,4BAAAA,OAAOmD,YAAY,CAACgX,GAAG,qBAAvBna,0BAAyBoa,SAAS,KACpC,IAAIqB,sDAA0B,CAACzb,OAAOmD,YAAY,CAACgX,GAAG,CAACC,SAAS;YAClEtY,YACE,IAAI4Z,8CAAsB,CAAC;gBACzBta;YACF;YACF,CAACiB,YACC,CAACpC,OACD6B,YACA9B,OAAOmD,YAAY,CAACwY,WAAW,IAC/B,IAAIC,oCAAiB,CAAC5b,OAAOmD,YAAY,CAACwY,WAAW,KAAK;YAC5D/O;YACA,CAAC3M,OACCkC,gBACA,IAAI,AACF7G,CAAAA,QAAQ,sDAAqD,EAC7DuR,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACFhR,CAAAA,QAAQ,iDAAgD,EACxDgV,OAAO,CAAC;gBACR3P;gBACA,GAAGX,OAAOmD,YAAY,CAACoJ,mBAAmB;YAC5C;SACH,CAAClQ,MAAM,CAACiG;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAIlC,mBAAmB,CAACA,gBAAgByb,UAAU,EAAE;YAClD/d,gCAAAA;SAAAA,0BAAAA,cAAcD,OAAO,sBAArBC,iCAAAA,wBAAuBgB,OAAO,qBAA9BhB,+BAAgCiH,IAAI,CAAC3E,gBAAgB0b,OAAO;IAC9D;KAIAhe,yBAAAA,cAAcD,OAAO,sBAArBC,iCAAAA,uBAAuBsM,OAAO,qBAA9BtM,+BAAgCie,OAAO,CACrC,IAAIC,wCAAmB,CACrB9b,CAAAA,6BAAAA,6BAAAA,SAAUkH,eAAe,qBAAzBlH,2BAA2B6K,KAAK,KAAI,CAAC,GACrC3K;IAIJ,MAAMuB,iBAAiB7D;IAEvB,IAAImE,cAAc;YAChBN,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAe9E,MAAM,sBAArB8E,+BAAAA,uBAAuB1D,KAAK,qBAA5B0D,6BAA8Boa,OAAO,CAAC;YACpC3W,MAAM;YACNxH,QAAQ;YACRd,MAAM;YACN8X,eAAe;QACjB;SACAjT,0BAAAA,eAAe9E,MAAM,sBAArB8E,gCAAAA,wBAAuB1D,KAAK,qBAA5B0D,8BAA8Boa,OAAO,CAAC;YACpC7F,YAAY;YACZtY,QAAQ;YACRd,MAAM;YACN4T,OAAO9I,yBAAc,CAACqU,SAAS;QACjC;SACAta,0BAAAA,eAAe9E,MAAM,sBAArB8E,gCAAAA,wBAAuB1D,KAAK,qBAA5B0D,8BAA8Boa,OAAO,CAAC;YACpC7N,aAAatG,yBAAc,CAACqU,SAAS;YACrCnf,MAAM;QACR;IACF;IAEA6E,eAAeua,WAAW,GAAG;QAC3BC,QAAQ;QACRC,iBAAiB;QACjBC,WAAW/d,MAAMC,OAAO,CAACyB,OAAOmD,YAAY,CAACmZ,UAAU,IACnD;YACEC,aAAavc,OAAOmD,YAAY,CAACmZ,UAAU;YAC3CE,eAAe7Y,aAAI,CAACC,IAAI,CAAC7D,KAAK;YAC9B0c,kBAAkB9Y,aAAI,CAACC,IAAI,CAAC7D,KAAK;QACnC,IACAC,OAAOmD,YAAY,CAACmZ,UAAU,GAC5B;YACEE,eAAe7Y,aAAI,CAACC,IAAI,CAAC7D,KAAK;YAC9B0c,kBAAkB9Y,aAAI,CAACC,IAAI,CAAC7D,KAAK;YACjC,GAAGC,OAAOmD,YAAY,CAACmZ,UAAU;QACnC,IACAtY;IACR;IAEArC,eAAe9E,MAAM,CAAE6Y,MAAM,GAAG;QAC9BgH,YAAY;YACV/G,KAAK;QACP;IACF;IACAhU,eAAe9E,MAAM,CAAE8f,SAAS,GAAG;QACjCC,OAAO;YACLpM,UAAU;QACZ;IACF;IAEA,IAAI,CAAC7O,eAAesR,MAAM,EAAE;QAC1BtR,eAAesR,MAAM,GAAG,CAAC;IAC3B;IACA,IAAInR,UAAU;QACZH,eAAesR,MAAM,CAAC4J,YAAY,GAAG;IACvC;IAEA,IAAI/a,YAAYG,cAAc;QAC5BN,eAAesR,MAAM,CAAC6J,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpDnb,eAAeob,QAAQ,GAAG,CAAC;IAC3B,IAAI/gB,QAAQgL,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAeob,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLrb,eAAeob,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAIhhB,QAAQgL,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAeob,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAIhd,KAAK;QACP,IAAI,CAAC0B,eAAekN,YAAY,EAAE;YAChClN,eAAekN,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAAC7L,WAAW;YACdrB,eAAekN,YAAY,CAACqO,eAAe,GAAG;QAChD;QACAvb,eAAekN,YAAY,CAACsO,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChCzY,sBAAsB,EAAE7E,2BAAAA,wBAAAA,OAAQmD,YAAY,qBAApBnD,sBAAsB6E,sBAAsB;QACpE2G,aAAaxL,OAAOwL,WAAW;QAC/BtC,gBAAgBA;QAChBqU,eAAevd,OAAOud,aAAa;QACnCC,uBACExd,OAAOyd,aAAa,KAAK,QACrBzZ,YACAhE,OAAOyd,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAAC3d,OAAO2d,2BAA2B;QACjEC,iBAAiB5d,OAAO4d,eAAe;QACvCC,aAAa7d,OAAOmD,YAAY,CAAC0a,WAAW;QAC5CC,mBAAmB9d,OAAOmD,YAAY,CAAC2a,iBAAiB;QACxDC,mBAAmB/d,OAAOmD,YAAY,CAAC4a,iBAAiB;QACxD3a,aAAapD,OAAOmD,YAAY,CAACC,WAAW;QAC5CgT,UAAUpW,OAAOoW,QAAQ;QACzBoD,6BAA6BxZ,OAAOwZ,2BAA2B;QAC/DrG,aAAanT,OAAOmT,WAAW;QAC/BlQ;QACA+W,eAAe/X;QACflB;QACA0X,SAAS,CAAC,CAACzY,OAAOyY,OAAO;QACzB7V;QACAob,WAAWna;QACXqJ,aAAa,GAAElN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBkN,aAAa;QAC7CD,qBAAqB,GAAEjN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBiN,qBAAqB;QAC7DD,gBAAgB,GAAEhN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBgN,gBAAgB;QACnDD,KAAK,GAAE/M,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiB+M,KAAK;QAC7BK,OAAO,GAAEpN,oBAAAA,OAAOgF,QAAQ,qBAAfhF,kBAAiBoN,OAAO;QACjClG,mBAAmBlH,OAAOkH,iBAAiB;QAC3C+W,iBAAiBje,OAAO8V,MAAM,CAACoI,UAAU;QACzCC,qBAAqBne,OAAOmD,YAAY,CAACgb,mBAAmB;QAC5DC,kBAAkBpe,OAAOmD,YAAY,CAACib,gBAAgB;QACtDtX,yBAAyBpG;IAC3B;IAEA,MAAM2d,QAAa;QACjBvhB,MAAM;QACN,mFAAmF;QACnFwhB,sBAAsBre,MAAM,IAAIse;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjD7iB,SAAS,GAAG0Y,UAAU,CAAC,EAAEpY,QAAQC,GAAG,CAAC8e,cAAc,CAAC,CAAC,EAAEqC,YAAY;QACnEoB,gBAAgB7a,aAAI,CAACC,IAAI,CAACF,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClE+a,aAAaxe,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAOyY,OAAO,IAAIzY,OAAOuK,UAAU,EAAE;QACvC8T,MAAMK,iBAAiB,GAAG;YACxB1e,QAAQ;gBAACA,OAAOuK,UAAU;aAAC;YAC3B,uGAAuG;YACvGoU,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLN,MAAMK,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACAhd,0BAAAA,eAAeyI,OAAO,qBAAtBzI,wBAAwBoD,IAAI,CAAC,CAACC;QAC5BA,SAAS4Z,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAActb,aAAI,CAACub,OAAO,CAAC5jB,QAAQuC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAMshB,OAAOT,kBAAmB;gBACnC,IAAIS,IAAIrO,UAAU,CAACmO,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEAxd,eAAe0c,KAAK,GAAGA;IAEvB,IAAIriB,QAAQC,GAAG,CAACojB,oBAAoB,EAAE;QACpC,MAAMC,QAAQtjB,QAAQC,GAAG,CAACojB,oBAAoB,CAACva,QAAQ,CAAC;QACxD,MAAMya,gBACJvjB,QAAQC,GAAG,CAACojB,oBAAoB,CAACva,QAAQ,CAAC;QAC5C,MAAM0a,gBACJxjB,QAAQC,GAAG,CAACojB,oBAAoB,CAACva,QAAQ,CAAC;QAC5C,MAAM2a,gBACJzjB,QAAQC,GAAG,CAACojB,oBAAoB,CAACva,QAAQ,CAAC;QAC5C,MAAM4a,gBACJ1jB,QAAQC,GAAG,CAACojB,oBAAoB,CAACva,QAAQ,CAAC;QAE5C,MAAM6a,UACJ,AAACJ,iBAAiBzd,YAAc0d,iBAAiB7c;QACnD,MAAMid,UACJ,AAACH,iBAAiB3d,YAAc4d,iBAAiB/c;QAEnD,MAAMkd,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvB3d,eAAeme,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzBhe,eAAeyI,OAAO,CAAErF,IAAI,CAAC,CAACC;gBAC5BA,SAAS4Z,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C7hB,QAAQ+iB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClBje,eAAeyI,OAAO,CAAErF,IAAI,CAAC,CAACC;gBAC5BA,SAAS4Z,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C7hB,QAAQ+iB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJ9H,gBAAO,CAAC8H,cAAc;YACxB5e,eAAeyI,OAAO,CAAErF,IAAI,CAC1B,IAAIwb,eAAe;gBACjBZ,SAAS;YACX;YAEFhe,eAAege,OAAO,GAAG;QAC3B;IACF;IAEA7hB,gBAAgB,MAAM0iB,IAAAA,0BAAkB,EAAC1iB,eAAe;QACtDwC;QACAmgB,eAAe1gB;QACf2gB,eAAe5f,WACX,IAAI+K,OAAO8U,IAAAA,gCAAkB,EAAChd,aAAI,CAACC,IAAI,CAAC9C,UAAU,CAAC,IAAI,CAAC,MACxDkD;QACJhB;QACA4d,eAAe3gB;QACfyG,UAAU/D;QACVqX,eAAe/X;QACf4e,WAAW/e,YAAYG;QACvBkR,aAAanT,OAAOmT,WAAW,IAAI;QACnC2N,aAAa9gB,OAAO8gB,WAAW;QAC/BnD,6BAA6B3d,OAAO2d,2BAA2B;QAC/DoD,QAAQ/gB,OAAO+gB,MAAM;QACrB5d,cAAcnD,OAAOmD,YAAY;QACjC4S,qBAAqB/V,OAAO8V,MAAM,CAACC,mBAAmB;QACtDrR,mBAAmB1E,OAAO0E,iBAAiB;QAC3C0Z,kBAAkBpe,OAAOmD,YAAY,CAACib,gBAAgB;IACxD;IAEA,0BAA0B;IAC1BtgB,cAAcugB,KAAK,CAAChT,IAAI,GAAG,GAAGvN,cAAcuN,IAAI,CAAC,CAAC,EAAEvN,cAAckjB,IAAI,GACpEngB,gBAAgB,cAAc,IAC9B;IAEF,IAAIZ,KAAK;QACP,IAAInC,cAAcjB,MAAM,EAAE;YACxBiB,cAAcjB,MAAM,CAACokB,WAAW,GAAG,CAACpkB,UAClC,CAAC2D,mBAAmB4E,IAAI,CAACvI,QAAO+T,QAAQ;QAC5C,OAAO;YACL9S,cAAcjB,MAAM,GAAG;gBACrBokB,aAAa,CAACpkB,UAAgB,CAAC2D,mBAAmB4E,IAAI,CAACvI,QAAO+T,QAAQ;YACxE;QACF;IACF;IAEA,IAAIsQ,kBAAkBpjB,cAAcb,OAAO;IAC3C,IAAI,OAAO+C,OAAOyY,OAAO,KAAK,YAAY;YACd3a,wBA0CtB6D,6BAKKA;QA/CT,MAAMwf,qBAAoBrjB,yBAAAA,cAAcsM,OAAO,qBAArBtM,uBAAuBgF,MAAM;QAEvDhF,gBAAgBkC,OAAOyY,OAAO,CAAC3a,eAAe;YAC5CiC;YACAE;YACAyG,UAAU/D;YACVlC;YACAT;YACAoI;YACAgZ,YAAY5kB,OAAO8O,IAAI,CAAC1K,aAAakC,MAAM;YAC3C2V,SAAAA,gBAAO;YACP,GAAI9V,0BACA;gBACE0e,aAAapf,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAI2K,mBAAmBuU,mBAAmB;gBACfrjB;YAAzB,MAAMwjB,oBAAmBxjB,0BAAAA,cAAcsM,OAAO,qBAArBtM,wBAAuBgF,MAAM;YACtD,IAAIwe,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5CvU,gBAAgB4U,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAACzjB,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAInC,MACR,CAAC,6GAA6G,EAAEqE,OAAOyhB,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIxhB,OAAOihB,oBAAoBpjB,cAAcb,OAAO,EAAE;YACpDa,cAAcb,OAAO,GAAGikB;YACxBnkB,qBAAqBmkB;QACvB;QAEA,wDAAwD;QACxD,MAAMvf,iBAAiB7D;QAEvB,0EAA0E;QAC1E,IAAI6D,EAAAA,8BAAAA,eAAeua,WAAW,qBAA1Bva,4BAA4B+f,eAAe,MAAK,MAAM;YACxD/f,eAAeua,WAAW,CAACwF,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAOhgB,+BAAAA,eAAeua,WAAW,qBAA1Bva,6BAA4B+f,eAAe,MAAK,YACvD/f,eAAeua,WAAW,CAACwF,eAAe,CAACC,OAAO,KAAK,OACvD;YACAhgB,eAAeua,WAAW,CAACwF,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAAC7jB,cAAsB8jB,IAAI,KAAK,YAAY;YACrD1kB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMc,QAAQH,EAAAA,wBAAAA,cAAcjB,MAAM,qBAApBiB,sBAAsBG,KAAK,KAAI,EAAE;IAE/C,MAAM4jB,gBAAgB5jB,MAAM6jB,IAAI,CAC9B,CAAC3jB,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKP,MAAM,KAAK,uBAChB,UAAUO,QACVA,KAAKiH,IAAI,YAAYyG,UACrB1N,KAAKiH,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAIyc,iBAAiB7e,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C/E,MAAM8G,IAAI,CAAC;YACTK,MAAMyc,cAAczc,IAAI;YACxBqQ,OAAO;gBACL7N,yBAAc,CAACC,qBAAqB;gBACpCD,yBAAc,CAACI,mBAAmB;gBAClCJ,yBAAc,CAACM,eAAe;aAC/B,CAAC4D,GAAG,CAAC,CAAC4E,QAAW,CAAA;oBAChBxC,aAAawC;oBACb7S,SAAS;wBACPiC,OAAOyV,IAAAA,uCAAgB,EAAClS,qBAAqB;4BAC3CtC;4BACA2P;4BACAzO;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAACjC,OAAO8V,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAMgM,gBAAgB9jB,MAAM6jB,IAAI,CAC9B,CAAC3jB,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKP,MAAM,KAAK;QAExD,IAAIikB,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAc3c,IAAI,GAAG;QACvB;IACF;IAEA,IACEpF,OAAOmD,YAAY,CAAC6e,SAAS,MAC7BlkB,yBAAAA,cAAcjB,MAAM,qBAApBiB,uBAAsBG,KAAK,KAC3BH,cAAcsM,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAM6X,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjB3c,SAAS0c;YACTjM,QAAQiM;YACRnlB,MAAM;QACR;QAEA,MAAMqlB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMjkB,QAAQL,cAAcjB,MAAM,CAACoB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKN,OAAO,EAAE;gBAChBskB,SAASpd,IAAI,CAAC5G;YAChB,OAAO;gBACL,IACEA,KAAKsX,KAAK,IACV,CAAEtX,CAAAA,KAAKiH,IAAI,IAAIjH,KAAKoH,OAAO,IAAIpH,KAAKyS,QAAQ,IAAIzS,KAAK6X,MAAM,AAAD,GAC1D;oBACA7X,KAAKsX,KAAK,CAACvX,OAAO,CAAC,CAACO,IAAM2jB,WAAWrd,IAAI,CAACtG;gBAC5C,OAAO;oBACL2jB,WAAWrd,IAAI,CAAC5G;gBAClB;YACF;QACF;QAEAL,cAAcjB,MAAM,CAACoB,KAAK,GAAG;eACvBkkB;YACJ;gBACE1M,OAAO;uBAAI2M;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAOliB,OAAOqiB,oBAAoB,KAAK,YAAY;QACrD,MAAM5b,UAAUzG,OAAOqiB,oBAAoB,CAAC;YAC1CvP,cAAchV,cAAcgV,YAAY;QAC1C;QACA,IAAIrM,QAAQqM,YAAY,EAAE;YACxBhV,cAAcgV,YAAY,GAAGrM,QAAQqM,YAAY;QACnD;IACF;IAEA,SAASwP,YAAYnkB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAMokB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAIpkB,gBAAgB0N,UAAU0W,UAAU/jB,IAAI,CAAC,CAACgkB,QAAUrkB,KAAKiH,IAAI,CAACod,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAOrkB,SAAS,YAAY;YAC9B,IACEokB,UAAU/jB,IAAI,CAAC,CAACgkB;gBACd,IAAI;oBACF,IAAIrkB,KAAKqkB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAIlkB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAAC8jB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJ3kB,EAAAA,yBAAAA,cAAcjB,MAAM,sBAApBiB,8BAAAA,uBAAsBG,KAAK,qBAA3BH,4BAA6BU,IAAI,CAC/B,CAACL,OAAcmkB,YAAYnkB,KAAKiH,IAAI,KAAKkd,YAAYnkB,KAAKmH,OAAO,OAC9D;IAEP,IAAImd,kBAAkB;YAYhB3kB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAI6E,yBAAyB;YAC3BzF,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EACF,8FAEF;QAEN;QAEA,KAAIS,yBAAAA,cAAcjB,MAAM,sBAApBiB,+BAAAA,uBAAsBG,KAAK,qBAA3BH,6BAA6BgF,MAAM,EAAE;YACvC,6BAA6B;YAC7BhF,cAAcjB,MAAM,CAACoB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAEgX,KAAK,GAAG;oBAC1BhX,EAAEgX,KAAK,GAAGhX,EAAEgX,KAAK,CAACpZ,MAAM,CACtB,CAACqmB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAI9kB,0BAAAA,cAAcsM,OAAO,qBAArBtM,wBAAuBgF,MAAM,EAAE;YACjC,gCAAgC;YAChChF,cAAcsM,OAAO,GAAGtM,cAAcsM,OAAO,CAAC/N,MAAM,CAClD,CAACC,IAAM,AAACA,EAAUumB,iBAAiB,KAAK;QAE5C;QACA,KAAI/kB,8BAAAA,cAAc+Q,YAAY,sBAA1B/Q,wCAAAA,4BAA4B+T,SAAS,qBAArC/T,sCAAuCgF,MAAM,EAAE;YACjD,uBAAuB;YACvBhF,cAAc+Q,YAAY,CAACgD,SAAS,GAClC/T,cAAc+Q,YAAY,CAACgD,SAAS,CAACxV,MAAM,CACzC,CAACymB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAI5iB,OAAO6B,UAAU;QACnB/G,mBAAmB+C,eAAesK,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAM0a,gBAAqBjlB,cAAc0L,KAAK;IAC9C,IAAI,OAAOuZ,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAMxZ,QACJ,OAAOuZ,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEtZ,iBACAnL,MAAMC,OAAO,CAACiL,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAAC1G,MAAM,GAAG,GAC1B;gBACA,MAAMmgB,eAAexZ,aAAa,CAChCK,4CAAgC,CACjC;gBACDN,KAAK,CAACM,4CAAgC,CAAC,GAAG;uBACrCN,KAAK,CAAC,UAAU;oBACnByZ;iBACD;YACH;YACA,OAAOzZ,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAM6B,QAAQ7O,OAAO8O,IAAI,CAAC9B,OAAQ;gBACrCA,KAAK,CAAC6B,KAAK,GAAG6X,IAAAA,2BAAkB,EAAC;oBAC/BC,OAAO3Z,KAAK,CAAC6B,KAAK;oBAClB1K;oBACA0K;oBACArI;gBACF;YACF;YAEA,OAAOwG;QACT;QACA,sCAAsC;QACtC1L,cAAc0L,KAAK,GAAGwZ;IACxB;IAEA,IAAI,CAAC/iB,OAAO,OAAOnC,cAAc0L,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7B1L,cAAc0L,KAAK,GAAG,MAAM1L,cAAc0L,KAAK;IACjD;IAEA,OAAO1L;AACT"}