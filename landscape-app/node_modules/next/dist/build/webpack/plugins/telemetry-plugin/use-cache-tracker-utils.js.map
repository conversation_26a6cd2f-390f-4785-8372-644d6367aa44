{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/telemetry-plugin/use-cache-tracker-utils.ts"], "sourcesContent": ["export type UseCacheTrackerKey = `useCache/${string}`\n\nexport const createUseCacheTracker = () => new Map<UseCacheTrackerKey, number>()\n\n/**\n * Example usage:\n *\n * const tracker1 = { 'useCache/file1': 1, 'useCache/file2': 2 };\n * const tracker2 = { 'useCache/file2': 3, 'useCache/file3': 4 };\n * const merged = mergeUseCacheTrackers(tracker1, tracker2);\n *\n * // Result: { 'useCache/file1': 1, 'useCache/file2': 5, 'useCache/file3': 4 }\n */\nexport const mergeUseCacheTrackers = (\n  tracker1: Record<UseCacheTrackerKey, number> | undefined,\n  tracker2: Record<UseCacheTrackerKey, number> | undefined\n): Record<UseCacheTrackerKey, number> => {\n  const mergedTracker: Record<UseCacheTrackerKey, number> = { ...tracker1 }\n\n  if (tracker2) {\n    for (const key in tracker2) {\n      if (Object.prototype.hasOwnProperty.call(tracker2, key)) {\n        const typedKey = key as UseCacheTrackerKey\n        if (mergedTracker[typedKey] !== undefined) {\n          mergedTracker[typedKey] += tracker2[typedKey]\n        } else {\n          mergedTracker[typedKey] = tracker2[typedKey]\n        }\n      }\n    }\n  }\n\n  return mergedTracker\n}\n"], "names": ["createUseCacheTracker", "mergeUseCacheTrackers", "Map", "tracker1", "tracker2", "mergedTracker", "key", "Object", "prototype", "hasOwnProperty", "call", "<PERSON><PERSON><PERSON>", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB;eAArBA;;IAWAC,qBAAqB;eAArBA;;;AAXN,MAAMD,wBAAwB,IAAM,IAAIE;AAWxC,MAAMD,wBAAwB,CACnCE,UACAC;IAEA,MAAMC,gBAAoD;QAAE,GAAGF,QAAQ;IAAC;IAExE,IAAIC,UAAU;QACZ,IAAK,MAAME,OAAOF,SAAU;YAC1B,IAAIG,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,UAAUE,MAAM;gBACvD,MAAMK,WAAWL;gBACjB,IAAID,aAAa,CAACM,SAAS,KAAKC,WAAW;oBACzCP,aAAa,CAACM,SAAS,IAAIP,QAAQ,CAACO,SAAS;gBAC/C,OAAO;oBACLN,aAAa,CAACM,SAAS,GAAGP,QAAQ,CAACO,SAAS;gBAC9C;YACF;QACF;IACF;IAEA,OAAON;AACT"}