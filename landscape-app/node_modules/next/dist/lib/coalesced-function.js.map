{"version": 3, "sources": ["../../src/lib/coalesced-function.ts"], "sourcesContent": ["type CoalescedInvoke<T> = {\n  isOrigin: boolean\n  value: T\n}\n\nexport type UnwrapPromise<T> = T extends Promise<infer U> ? U : T\n\nconst globalInvokeCache = new Map<string, Promise<CoalescedInvoke<unknown>>>()\n\nexport function withCoalescedInvoke<F extends (...args: any) => any>(\n  func: F\n): (\n  key: string,\n  args: Parameters<F>\n) => Promise<CoalescedInvoke<UnwrapPromise<ReturnType<F>>>> {\n  return async function (key: string, args: Parameters<F>) {\n    const entry = globalInvokeCache.get(key)\n    if (entry) {\n      return entry.then((res) => ({\n        isOrigin: false,\n        value: res.value as UnwrapPromise<ReturnType<F>>,\n      }))\n    }\n\n    async function __wrapper() {\n      return await func.apply(undefined, args)\n    }\n\n    const future = __wrapper()\n      .then((res) => {\n        globalInvokeCache.delete(key)\n        return { isOrigin: true, value: res as UnwrapPromise<ReturnType<F>> }\n      })\n      .catch((err) => {\n        globalInvokeCache.delete(key)\n        return Promise.reject(err)\n      })\n    globalInvokeCache.set(key, future)\n    return future\n  }\n}\n"], "names": ["withCoalescedInvoke", "globalInvokeCache", "Map", "func", "key", "args", "entry", "get", "then", "res", "is<PERSON><PERSON>in", "value", "__wrapper", "apply", "undefined", "future", "delete", "catch", "err", "Promise", "reject", "set"], "mappings": ";;;;+BASgBA;;;eAAAA;;;AAFhB,MAAMC,oBAAoB,IAAIC;AAEvB,SAASF,oBACdG,IAAO;IAKP,OAAO,eAAgBC,GAAW,EAAEC,IAAmB;QACrD,MAAMC,QAAQL,kBAAkBM,GAAG,CAACH;QACpC,IAAIE,OAAO;YACT,OAAOA,MAAME,IAAI,CAAC,CAACC,MAAS,CAAA;oBAC1BC,UAAU;oBACVC,OAAOF,IAAIE,KAAK;gBAClB,CAAA;QACF;QAEA,eAAeC;YACb,OAAO,MAAMT,KAAKU,KAAK,CAACC,WAAWT;QACrC;QAEA,MAAMU,SAASH,YACZJ,IAAI,CAAC,CAACC;YACLR,kBAAkBe,MAAM,CAACZ;YACzB,OAAO;gBAAEM,UAAU;gBAAMC,OAAOF;YAAoC;QACtE,GACCQ,KAAK,CAAC,CAACC;YACNjB,kBAAkBe,MAAM,CAACZ;YACzB,OAAOe,QAAQC,MAAM,CAACF;QACxB;QACFjB,kBAAkBoB,GAAG,CAACjB,KAAKW;QAC3B,OAAOA;IACT;AACF"}