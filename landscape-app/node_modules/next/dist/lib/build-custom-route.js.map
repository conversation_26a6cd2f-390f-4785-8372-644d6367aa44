{"version": 3, "sources": ["../../src/lib/build-custom-route.ts"], "sourcesContent": ["import { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type {\n  ManifestHeaderRoute,\n  ManifestRedirectRoute,\n  ManifestRewriteRoute,\n} from '../build'\nimport {\n  normalizeRouteRegex,\n  type Header,\n  type Redirect,\n  type Rewrite,\n  type RouteType,\n} from './load-custom-routes'\nimport { getRedirectStatus, modifyRouteRegex } from './redirect-status'\n\nexport function buildCustomRoute(\n  type: 'header',\n  route: Header\n): ManifestHeaderRoute\nexport function buildCustomRoute(\n  type: 'rewrite',\n  route: Rewrite\n): ManifestRewriteRoute\nexport function buildCustomRoute(\n  type: 'redirect',\n  route: Redirect,\n  restrictedRedirectPaths: string[]\n): ManifestRedirectRoute\nexport function buildCustomRoute(\n  type: RouteType,\n  route: Redirect | Rewrite | Header,\n  restrictedRedirectPaths?: string[]\n): ManifestHeaderRoute | ManifestRewriteRoute | ManifestRedirectRoute {\n  const compiled = pathToRegexp(route.source, [], {\n    strict: true,\n    sensitive: false,\n    delimiter: '/', // default is `/#?`, but Next does not pass query info\n  })\n\n  let source = compiled.source\n  if (!route.internal) {\n    source = modifyRouteRegex(\n      source,\n      type === 'redirect' ? restrictedRedirectPaths : undefined\n    )\n  }\n\n  const regex = normalizeRouteRegex(source)\n\n  if (type !== 'redirect') {\n    return { ...route, regex }\n  }\n\n  return {\n    ...route,\n    statusCode: getRedirectStatus(route as Redirect),\n    permanent: undefined,\n    regex,\n  }\n}\n"], "names": ["buildCustomRoute", "type", "route", "restrictedRedirectPaths", "compiled", "pathToRegexp", "source", "strict", "sensitive", "delimiter", "internal", "modifyRouteRegex", "undefined", "regex", "normalizeRouteRegex", "statusCode", "getRedirectStatus", "permanent"], "mappings": ";;;;+BA4BgBA;;;eAAAA;;;8BA5Ba;kCAYtB;gCAC6C;AAe7C,SAASA,iBACdC,IAAe,EACfC,KAAkC,EAClCC,uBAAkC;IAElC,MAAMC,WAAWC,IAAAA,0BAAY,EAACH,MAAMI,MAAM,EAAE,EAAE,EAAE;QAC9CC,QAAQ;QACRC,WAAW;QACXC,WAAW;IACb;IAEA,IAAIH,SAASF,SAASE,MAAM;IAC5B,IAAI,CAACJ,MAAMQ,QAAQ,EAAE;QACnBJ,SAASK,IAAAA,gCAAgB,EACvBL,QACAL,SAAS,aAAaE,0BAA0BS;IAEpD;IAEA,MAAMC,QAAQC,IAAAA,qCAAmB,EAACR;IAElC,IAAIL,SAAS,YAAY;QACvB,OAAO;YAAE,GAAGC,KAAK;YAAEW;QAAM;IAC3B;IAEA,OAAO;QACL,GAAGX,KAAK;QACRa,YAAYC,IAAAA,iCAAiB,EAACd;QAC9Be,WAAWL;QACXC;IACF;AACF"}