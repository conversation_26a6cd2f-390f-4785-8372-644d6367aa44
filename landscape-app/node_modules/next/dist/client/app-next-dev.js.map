{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\n\nimport { appBootstrap } from './app-bootstrap'\nimport { initializeDevBuildIndicatorForAppRouter } from './dev/dev-build-indicator/initialize-for-app-router'\n\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate(instrumentationHooks)\n  initializeDevBuildIndicatorForAppRouter()\n})\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "require", "appBootstrap", "hydrate", "initializeDevBuildIndicatorForAppRouter"], "mappings": "AAAA,8BAA8B;;;;;QAEvB;8BAEsB;wCAC2B;AAExD,MAAMA,uBAAuBC,QAAQ;AAErCC,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGF,QAAQ;IAC5BE,QAAQH;IACRI,IAAAA,+DAAuC;AACzC"}