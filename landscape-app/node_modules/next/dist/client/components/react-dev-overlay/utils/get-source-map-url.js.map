{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/get-source-map-url.ts"], "sourcesContent": ["export function getSourceMapUrl(fileContents: string): string | null {\n  const regex = /\\/\\/[#@] ?sourceMappingURL=([^\\s'\"]+)\\s*$/gm\n  let match = null\n  for (;;) {\n    let next = regex.exec(fileContents)\n    if (next == null) {\n      break\n    }\n    match = next\n  }\n  if (!(match && match[1])) {\n    return null\n  }\n  return match[1].toString()\n}\n"], "names": ["getSourceMapUrl", "fileContents", "regex", "match", "next", "exec", "toString"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,YAAoB;IAClD,MAAMC,QAAQ;IACd,IAAIC,QAAQ;IACZ,OAAS;QACP,IAAIC,OAAOF,MAAMG,IAAI,CAACJ;QACtB,IAAIG,QAAQ,MAAM;YAChB;QACF;QACAD,QAAQC;IACV;IACA,IAAI,CAAED,CAAAA,SAASA,KAAK,CAAC,EAAE,AAAD,GAAI;QACxB,OAAO;IACT;IACA,OAAOA,KAAK,CAAC,EAAE,CAACG,QAAQ;AAC1B"}