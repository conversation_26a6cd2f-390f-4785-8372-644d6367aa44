{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport { getSocketUrl } from '../utils/get-socket-url'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport GlobalError from '../../error-boundary'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\n\nconst noop = () => {}\n\n// if an error is thrown while rendering an RSC stream, this will catch it in dev\n// and show the error overlay\nexport function createRootLevelDevOverlayElement(reactEl: React.ReactElement) {\n  const socketUrl = getSocketUrl(process.env.__NEXT_ASSET_PREFIX || '')\n  const socket = new window.WebSocket(`${socketUrl}/_next/webpack-hmr`)\n\n  // add minimal \"hot reload\" support for RSC errors\n  const handler = (event: MessageEvent) => {\n    let obj\n    try {\n      obj = JSON.parse(event.data)\n    } catch {}\n\n    if (!obj || !('action' in obj)) {\n      return\n    }\n\n    if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n      window.location.reload()\n    }\n  }\n\n  socket.addEventListener('message', handler)\n\n  return (\n    <AppDevOverlayErrorBoundary\n      globalError={[GlobalError, null]}\n      onError={noop}\n    >\n      {reactEl}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["createRootLevelDevOverlayElement", "noop", "reactEl", "socketUrl", "getSocketUrl", "process", "env", "__NEXT_ASSET_PREFIX", "socket", "window", "WebSocket", "handler", "event", "obj", "JSON", "parse", "data", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_COMPONENT_CHANGES", "location", "reload", "addEventListener", "AppDevOverlayErrorBoundary", "globalError", "GlobalError", "onError"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;;;gEAVE;8BACW;kCACe;wEACpB;4CACmB;AAE3C,MAAMC,OAAO,KAAO;AAIb,SAASD,iCAAiCE,OAA2B;IAC1E,MAAMC,YAAYC,IAAAA,0BAAY,EAACC,QAAQC,GAAG,CAACC,mBAAmB,IAAI;IAClE,MAAMC,SAAS,IAAIC,OAAOC,SAAS,CAAC,AAAC,KAAEP,YAAU;IAEjD,kDAAkD;IAClD,MAAMQ,UAAU,CAACC;QACf,IAAIC;QACJ,IAAI;YACFA,MAAMC,KAAKC,KAAK,CAACH,MAAMI,IAAI;QAC7B,EAAE,UAAM,CAAC;QAET,IAAI,CAACH,OAAO,CAAE,CAAA,YAAYA,GAAE,GAAI;YAC9B;QACF;QAEA,IAAIA,IAAII,MAAM,KAAKC,6CAA2B,CAACC,wBAAwB,EAAE;YACvEV,OAAOW,QAAQ,CAACC,MAAM;QACxB;IACF;IAEAb,OAAOc,gBAAgB,CAAC,WAAWX;IAEnC,qBACE,qBAACY,sDAA0B;QACzBC,aAAa;YAACC,sBAAW;YAAE;SAAK;QAChCC,SAASzB;kBAERC;;AAGP"}