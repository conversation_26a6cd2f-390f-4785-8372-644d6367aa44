{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/cache-key.ts"], "sourcesContent": ["// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\n// Only functions in this module should be allowed to create CacheKeys.\nexport type NormalizedHref = Opaque<'NormalizedHref', string>\nexport type NormalizedSearch = Opaque<'NormalizedSearch', string>\nexport type NormalizedNextUrl = Opaque<'NormalizedNextUrl', string>\n\nexport type RouteCacheKey = Opaque<\n  'RouteCacheKey',\n  {\n    href: NormalizedHref\n    search: NormalizedSearch\n    nextUrl: NormalizedNextUrl | null\n\n    // TODO: Eventually the dynamic params will be added here, too.\n  }\n>\n\nexport function createCacheKey(\n  originalHref: string,\n  nextUrl: string | null\n): RouteCacheKey {\n  const originalUrl = new URL(originalHref)\n  const cacheKey = {\n    href: originalHref as NormalizedHref,\n    search: originalUrl.search as NormalizedSearch,\n    nextUrl: nextUrl as NormalizedNextUrl | null,\n  } as RouteCacheKey\n  return cacheKey\n}\n"], "names": ["createCacheKey", "originalHref", "nextUrl", "originalUrl", "URL", "cache<PERSON>ey", "href", "search"], "mappings": "AAAA,2DAA2D;;;;;+BAmB3CA;;;eAAAA;;;AAAT,SAASA,eACdC,YAAoB,EACpBC,OAAsB;IAEtB,MAAMC,cAAc,IAAIC,IAAIH;IAC5B,MAAMI,WAAW;QACfC,MAAML;QACNM,QAAQJ,YAAYI,MAAM;QAC1BL,SAASA;IACX;IACA,OAAOG;AACT"}