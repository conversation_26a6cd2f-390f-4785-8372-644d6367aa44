{"version": 3, "sources": ["../../../src/server/route-matcher-providers/app-page-route-matcher-provider.ts"], "sourcesContent": ["import { isAppPageRoute } from '../../lib/is-app-page-route'\n\nimport { APP_PATHS_MANIFEST } from '../../shared/lib/constants'\nimport { AppNormalizers } from '../normalizers/built/app'\nimport { RouteKind } from '../route-kind'\nimport { AppPageRouteMatcher } from '../route-matchers/app-page-route-matcher'\nimport type {\n  Manifest,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\n\nexport class AppPageRouteMatcherProvider extends ManifestRouteMatcherProvider<AppPageRouteMatcher> {\n  private readonly normalizers: AppNormalizers\n\n  constructor(distDir: string, manifestLoader: ManifestLoader) {\n    super(APP_PATHS_MANIFEST, manifestLoader)\n\n    this.normalizers = new AppNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<AppPageRouteMatcher>> {\n    // This matcher only matches app pages.\n    const pages = Object.keys(manifest).filter((page) => isAppPageRoute(page))\n\n    // Collect all the app paths for each page. This could include any parallel\n    // routes.\n    const allAppPaths: Record<string, string[]> = {}\n    for (const page of pages) {\n      const pathname = this.normalizers.pathname.normalize(page)\n      if (pathname in allAppPaths) allAppPaths[pathname].push(page)\n      else allAppPaths[pathname] = [page]\n    }\n\n    // Format the routes.\n    const matchers: Array<AppPageRouteMatcher> = []\n    for (const [pathname, appPaths] of Object.entries(allAppPaths)) {\n      // TODO-APP: (wyattjoh) this is a hack right now, should be more deterministic\n      const page = appPaths[0]\n\n      const filename = this.normalizers.filename.normalize(manifest[page])\n      const bundlePath = this.normalizers.bundlePath.normalize(page)\n\n      matchers.push(\n        new AppPageRouteMatcher({\n          kind: RouteKind.APP_PAGE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n          appPaths,\n        })\n      )\n    }\n\n    return matchers\n  }\n}\n"], "names": ["AppPageRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "APP_PATHS_MANIFEST", "normalizers", "AppNormalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "isAppPageRoute", "allAppPaths", "pathname", "normalize", "push", "matchers", "appPaths", "entries", "filename", "bundlePath", "AppPageRouteMatcher", "kind", "RouteKind", "APP_PAGE"], "mappings": ";;;;+BAYaA;;;eAAAA;;;gCAZkB;2BAEI;qBACJ;2BACL;qCACU;8CAKS;AAEtC,MAAMA,oCAAoCC,0DAA4B;IAG3EC,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACC,6BAAkB,EAAED;QAE1B,IAAI,CAACE,WAAW,GAAG,IAAIC,mBAAc,CAACJ;IACxC;IAEA,MAAgBK,UACdC,QAAkB,EAC2B;QAC7C,uCAAuC;QACvC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASC,IAAAA,8BAAc,EAACD;QAEpE,2EAA2E;QAC3E,UAAU;QACV,MAAME,cAAwC,CAAC;QAC/C,KAAK,MAAMF,QAAQJ,MAAO;YACxB,MAAMO,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACC,SAAS,CAACJ;YACrD,IAAIG,YAAYD,aAAaA,WAAW,CAACC,SAAS,CAACE,IAAI,CAACL;iBACnDE,WAAW,CAACC,SAAS,GAAG;gBAACH;aAAK;QACrC;QAEA,qBAAqB;QACrB,MAAMM,WAAuC,EAAE;QAC/C,KAAK,MAAM,CAACH,UAAUI,SAAS,IAAIV,OAAOW,OAAO,CAACN,aAAc;YAC9D,8EAA8E;YAC9E,MAAMF,OAAOO,QAAQ,CAAC,EAAE;YAExB,MAAME,WAAW,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACL,SAAS,CAACT,QAAQ,CAACK,KAAK;YACnE,MAAMU,aAAa,IAAI,CAAClB,WAAW,CAACkB,UAAU,CAACN,SAAS,CAACJ;YAEzDM,SAASD,IAAI,CACX,IAAIM,wCAAmB,CAAC;gBACtBC,MAAMC,oBAAS,CAACC,QAAQ;gBACxBX;gBACAH;gBACAU;gBACAD;gBACAF;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}