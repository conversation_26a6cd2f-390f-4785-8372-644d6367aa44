{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-data-route.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils/is-dynamic'\nimport { getNamedRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeRouteRegex } from '../../../lib/load-custom-routes'\nimport { escapeStringRegexp } from '../../../shared/lib/escape-regexp'\n\nexport function buildDataRoute(page: string, buildId: string) {\n  const pagePath = normalizePagePath(page)\n  const dataRoute = path.posix.join('/_next/data', buildId, `${pagePath}.json`)\n\n  let dataRouteRegex: string\n  let namedDataRouteRegex: string | undefined\n  let routeKeys: { [named: string]: string } | undefined\n\n  if (isDynamicRoute(page)) {\n    const routeRegex = getNamedRouteRegex(dataRoute, {\n      prefixRouteKeys: true,\n      includeSuffix: true,\n      excludeOptionalTrailingSlash: true,\n    })\n\n    dataRouteRegex = normalizeRouteRegex(routeRegex.re.source)\n    namedDataRouteRegex = routeRegex.namedRegex\n    routeKeys = routeRegex.routeKeys\n  } else {\n    dataRouteRegex = normalizeRouteRegex(\n      new RegExp(\n        `^${path.posix.join(\n          '/_next/data',\n          escapeStringRegexp(buildId),\n          `${pagePath}\\\\.json`\n        )}$`\n      ).source\n    )\n  }\n\n  return {\n    page,\n    routeKeys,\n    dataRouteRegex,\n    namedDataRouteRegex,\n  }\n}\n"], "names": ["buildDataRoute", "page", "buildId", "pagePath", "normalizePagePath", "dataRoute", "path", "posix", "join", "dataRouteRegex", "namedDataRouteRegex", "routeKeys", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "includeSuffix", "excludeOptionalTrailingSlash", "normalizeRouteRegex", "re", "source", "namedRegex", "RegExp", "escapeStringRegexp"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;6DAPC;mCACiB;2BACH;4BACI;kCACC;8BACD;;;;;;AAE5B,SAASA,eAAeC,IAAY,EAAEC,OAAe;IAC1D,MAAMC,WAAWC,IAAAA,oCAAiB,EAACH;IACnC,MAAMI,YAAYC,aAAI,CAACC,KAAK,CAACC,IAAI,CAAC,eAAeN,SAAS,GAAGC,SAAS,KAAK,CAAC;IAE5E,IAAIM;IACJ,IAAIC;IACJ,IAAIC;IAEJ,IAAIC,IAAAA,yBAAc,EAACX,OAAO;QACxB,MAAMY,aAAaC,IAAAA,8BAAkB,EAACT,WAAW;YAC/CU,iBAAiB;YACjBC,eAAe;YACfC,8BAA8B;QAChC;QAEAR,iBAAiBS,IAAAA,qCAAmB,EAACL,WAAWM,EAAE,CAACC,MAAM;QACzDV,sBAAsBG,WAAWQ,UAAU;QAC3CV,YAAYE,WAAWF,SAAS;IAClC,OAAO;QACLF,iBAAiBS,IAAAA,qCAAmB,EAClC,IAAII,OACF,CAAC,CAAC,EAAEhB,aAAI,CAACC,KAAK,CAACC,IAAI,CACjB,eACAe,IAAAA,gCAAkB,EAACrB,UACnB,GAAGC,SAAS,OAAO,CAAC,EACpB,CAAC,CAAC,EACJiB,MAAM;IAEZ;IAEA,OAAO;QACLnB;QACAU;QACAF;QACAC;IACF;AACF"}