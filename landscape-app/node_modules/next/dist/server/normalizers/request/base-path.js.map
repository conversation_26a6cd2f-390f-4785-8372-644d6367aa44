{"version": 3, "sources": ["../../../../src/server/normalizers/request/base-path.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { PrefixPathnameNormalizer } from './prefix'\n\nexport class BasePathPathnameNormalizer\n  extends PrefixPathnameNormalizer\n  implements PathnameNormalizer\n{\n  constructor(basePath: string) {\n    if (!basePath || basePath === '/') {\n      throw new Error('Invariant: basePath must be set and cannot be \"/\"')\n    }\n\n    super(basePath)\n  }\n}\n"], "names": ["BasePathPathnameNormalizer", "PrefixPathnameNormalizer", "constructor", "basePath", "Error"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAF4B;AAElC,MAAMA,mCACHC,gCAAwB;IAGhCC,YAAYC,QAAgB,CAAE;QAC5B,IAAI,CAACA,YAAYA,aAAa,KAAK;YACjC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA6D;QACrE;QAEA,KAAK,CAACD;IACR;AACF"}