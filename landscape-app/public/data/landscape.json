{"categories": [{"id": "container-runtime", "name": "容器运行时", "description": "容器化技术和运行时环境", "color": "#4F46E5"}, {"id": "orchestration", "name": "编排调度", "description": "容器编排和调度平台", "color": "#059669"}, {"id": "observability", "name": "可观测性", "description": "监控、日志和追踪工具", "color": "#DC2626"}, {"id": "security", "name": "安全", "description": "安全扫描和策略管理", "color": "#7C2D12"}, {"id": "storage", "name": "存储", "description": "数据存储和管理", "color": "#1D4ED8"}, {"id": "networking", "name": "网络", "description": "网络和服务网格", "color": "#9333EA"}], "architectureLayers": [{"id": "management-platform", "name": "ZStack 管理平台", "description": "统一管理控制台", "color": "#3B82F6", "backgroundColor": "#EBF4FF", "borderColor": "#3B82F6", "order": 1, "height": 120, "sections": [{"id": "zaku", "name": "ZStack Zaku 管理云平台", "backgroundColor": "#DBEAFE", "borderColor": "#3B82F6", "width": 0.45, "position": {"x": 0.02}, "projects": [{"id": "zstack-zaku", "name": "ZStack Zaku", "description": "企业级云管理平台", "color": "#3B82F6", "size": "large"}]}, {"id": "rds", "name": "ZStack RDS 数据库云平台", "backgroundColor": "#DBEAFE", "borderColor": "#3B82F6", "width": 0.45, "position": {"x": 0.52}, "projects": [{"id": "zstack-rds", "name": "ZStack RDS", "description": "数据库即服务平台", "color": "#3B82F6", "size": "large"}]}]}, {"id": "cloud-platform", "name": "ZStack Cloud 云平台", "description": "核心云服务平台", "color": "#10B981", "backgroundColor": "#ECFDF5", "borderColor": "#10B981", "order": 2, "height": 160, "sections": [{"id": "core-services", "name": "核心服务", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.15, "position": {"x": 0.02}, "projects": [{"id": "zstack-sds", "name": "ZStack SDS", "description": "软件定义存储", "color": "#10B981", "size": "medium"}]}, {"id": "compute", "name": "多租户", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.19}, "projects": [{"id": "multi-tenant", "name": "多租户", "description": "租户隔离管理", "color": "#10B981", "size": "small"}]}, {"id": "network", "name": "网络", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.33}, "projects": [{"id": "sdn", "name": "SDN", "description": "软件定义网络", "color": "#10B981", "size": "small"}]}, {"id": "cmp", "name": "CMP", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.47}, "projects": [{"id": "cmp", "name": "CMP", "description": "云管理平台", "color": "#10B981", "size": "small"}]}, {"id": "compute-mgmt", "name": "计算管理", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.61}, "projects": [{"id": "compute-mgmt", "name": "计算管理", "description": "虚拟机管理", "color": "#10B981", "size": "small"}]}, {"id": "monitoring", "name": "监控管理", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.75}, "projects": [{"id": "monitoring", "name": "监控管理", "description": "系统监控", "color": "#10B981", "size": "small"}]}, {"id": "vmware", "name": "VMware 管理", "backgroundColor": "#D1FAE5", "borderColor": "#10B981", "width": 0.12, "position": {"x": 0.89}, "projects": [{"id": "vmware-mgmt", "name": "VMware", "description": "VMware集成", "color": "#10B981", "size": "small"}]}]}, {"id": "virtualization", "name": "ZStack ZSphere虚拟化平台", "description": "虚拟化基础设施", "color": "#8B5CF6", "backgroundColor": "#F3E8FF", "borderColor": "#8B5CF6", "order": 3, "height": 140, "sections": [{"id": "hypervisor", "name": "管理器", "backgroundColor": "#E9D5FF", "borderColor": "#8B5CF6", "width": 0.2, "position": {"x": 0.02}, "projects": [{"id": "zsphere-mgmt", "name": "管理器", "description": "虚拟化管理", "color": "#8B5CF6", "size": "medium"}]}, {"id": "kvm", "name": "ZStack计算虚拟化", "backgroundColor": "#E9D5FF", "borderColor": "#8B5CF6", "width": 0.25, "position": {"x": 0.24}, "projects": [{"id": "kvm-virt", "name": "KVM虚拟化", "description": "KVM计算虚拟化", "color": "#8B5CF6", "size": "medium"}]}, {"id": "network-virt", "name": "ZStack网络虚拟化", "backgroundColor": "#E9D5FF", "borderColor": "#8B5CF6", "width": 0.25, "position": {"x": 0.51}, "projects": [{"id": "network-virt", "name": "网络虚拟化", "description": "网络虚拟化", "color": "#8B5CF6", "size": "medium"}]}, {"id": "storage-virt", "name": "ZStack存储虚拟化", "backgroundColor": "#E9D5FF", "borderColor": "#8B5CF6", "width": 0.25, "position": {"x": 0.78}, "projects": [{"id": "storage-virt", "name": "存储虚拟化", "description": "存储虚拟化", "color": "#8B5CF6", "size": "medium"}]}]}], "projects": [{"id": "docker", "name": "<PERSON>er", "description": "容器化平台，用于构建、分发和运行应用程序", "homepage_url": "https://docker.com", "github_url": "https://github.com/docker/docker", "category": "container-runtime", "tags": ["容器", "运行时", "开发工具"], "maturity": "graduated", "stars": 68000, "contributors": 2800, "license": "Apache-2.0", "language": "Go"}, {"id": "kubernetes", "name": "Kubernetes", "description": "开源容器编排平台，用于自动化部署、扩展和管理容器化应用", "homepage_url": "https://kubernetes.io", "github_url": "https://github.com/kubernetes/kubernetes", "category": "orchestration", "tags": ["编排", "调度", "集群管理"], "maturity": "graduated", "stars": 110000, "contributors": 6800, "license": "Apache-2.0", "language": "Go"}, {"id": "prometheus", "name": "Prometheus", "description": "开源监控和告警工具包，专为云原生环境设计", "homepage_url": "https://prometheus.io", "github_url": "https://github.com/prometheus/prometheus", "category": "observability", "tags": ["监控", "指标", "告警"], "maturity": "graduated", "stars": 55000, "contributors": 1900, "license": "Apache-2.0", "language": "Go"}, {"id": "grafana", "name": "<PERSON><PERSON>", "description": "开源分析和监控平台，支持多种数据源", "homepage_url": "https://grafana.com", "github_url": "https://github.com/grafana/grafana", "category": "observability", "tags": ["可视化", "仪表板", "分析"], "maturity": "graduated", "stars": 62000, "contributors": 2100, "license": "AGPL-3.0", "language": "TypeScript"}, {"id": "containerd", "name": "containerd", "description": "行业标准的容器运行时，专注于简单性、健壮性和可移植性", "homepage_url": "https://containerd.io", "github_url": "https://github.com/containerd/containerd", "category": "container-runtime", "tags": ["容器", "运行时", "CRI"], "maturity": "graduated", "stars": 17000, "contributors": 420, "license": "Apache-2.0", "language": "Go"}, {"id": "helm", "name": "<PERSON><PERSON>", "description": "Kubernetes的包管理器，简化应用程序的部署和管理", "homepage_url": "https://helm.sh", "github_url": "https://github.com/helm/helm", "category": "orchestration", "tags": ["包管理", "部署", "模板"], "maturity": "graduated", "stars": 27000, "contributors": 1200, "license": "Apache-2.0", "language": "Go"}, {"id": "istio", "name": "<PERSON><PERSON><PERSON>", "description": "开源服务网格，提供连接、保护、控制和观察服务的统一方式", "homepage_url": "https://istio.io", "github_url": "https://github.com/istio/istio", "category": "networking", "tags": ["服务网格", "安全", "流量管理"], "maturity": "graduated", "stars": 35000, "contributors": 1800, "license": "Apache-2.0", "language": "Go"}, {"id": "etcd", "name": "etcd", "description": "分布式可靠的键值存储，用于分布式系统的关键数据", "homepage_url": "https://etcd.io", "github_url": "https://github.com/etcd-io/etcd", "category": "storage", "tags": ["键值存储", "分布式", "一致性"], "maturity": "graduated", "stars": 47000, "contributors": 680, "license": "Apache-2.0", "language": "Go"}, {"id": "falco", "name": "Falco", "description": "云原生运行时安全项目，检测异常活动和潜在安全威胁", "homepage_url": "https://falco.org", "github_url": "https://github.com/falcosecurity/falco", "category": "security", "tags": ["安全", "运行时检测", "威胁检测"], "maturity": "graduated", "stars": 7200, "contributors": 380, "license": "Apache-2.0", "language": "C++"}, {"id": "jaeger", "name": "<PERSON><PERSON><PERSON>", "description": "开源端到端分布式追踪系统", "homepage_url": "https://jaegertracing.io", "github_url": "https://github.com/jaegertracing/jaeger", "category": "observability", "tags": ["分布式追踪", "性能监控", "微服务"], "maturity": "graduated", "stars": 20000, "contributors": 420, "license": "Apache-2.0", "language": "Go"}]}