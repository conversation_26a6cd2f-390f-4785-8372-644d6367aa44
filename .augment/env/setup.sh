#!/bin/bash

# 进入工作目录
cd /mnt/persist/workspace

# 创建Next.js项目
echo "创建完整的增强版Next.js landscape项目..."
npx create-next-app@latest landscape-app --typescript --tailwind --eslint --app --src-dir --import-alias "@/*" --no-turbopack --yes

cd landscape-app

# 安装依赖
npm install lucide-react clsx tailwind-merge

# 创建目录结构
mkdir -p src/components/ui
mkdir -p src/components/layout
mkdir -p src/lib
mkdir -p src/types
mkdir -p public/logos
mkdir -p public/data

# 创建所有必要文件
echo "创建类型定义..."
cat > src/types/landscape.ts << 'EOF'
export interface Project {
  id: string;
  name: string;
  description: string;
  homepage_url?: string;
  github_url?: string;
  logo?: string;
  category: string;
  subcategory?: string;
  tags: string[];
  maturity: 'sandbox' | 'incubating' | 'graduated' | 'archived';
  stars?: number;
  contributors?: number;
  license?: string;
  language?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  subcategories?: Subcategory[];
}

export interface Subcategory {
  id: string;
  name: string;
  description?: string;
  projects: Project[];
}

export interface LandscapeData {
  categories: Category[];
  projects: Project[];
}

export interface FilterState {
  search: string;
  category: string;
  maturity: string[];
  tags: string[];
}
EOF

echo "创建工具函数..."
cat > src/lib/utils.ts << 'EOF'
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function getMaturityColor(maturity: string): string {
  switch (maturity) {
    case 'graduated':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'incubating':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'sandbox':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'archived':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export function getMaturityLabel(maturity: string): string {
  switch (maturity) {
    case 'graduated':
      return '已毕业'
    case 'incubating':
      return '孵化中'
    case 'sandbox':
      return '沙盒'
    case 'archived':
      return '已归档'
    default:
      return maturity
  }
}
EOF

echo "创建UI组件..."
# 创建所有UI组件
cat > src/components/ui/ProjectCard.tsx << 'EOF'
'use client'

import { Project } from '@/types/landscape'
import { formatNumber, getMaturityColor, getMaturityLabel } from '@/lib/utils'
import { ExternalLink, Github, Star, Users } from 'lucide-react'

interface ProjectCardProps {
  project: Project
}

export function ProjectCard({ project }: ProjectCardProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-2xl font-bold text-gray-600">
              {project.name.charAt(0)}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getMaturityColor(project.maturity)}`}>
              {getMaturityLabel(project.maturity)}
            </span>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {project.homepage_url && (
            <a
              href={project.homepage_url}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
          {project.github_url && (
            <a
              href={project.github_url}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Github className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>

      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
        {project.description}
      </p>

      <div className="flex flex-wrap gap-2 mb-4">
        {project.tags.slice(0, 3).map((tag) => (
          <span
            key={tag}
            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
          >
            {tag}
          </span>
        ))}
        {project.tags.length > 3 && (
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
            +{project.tags.length - 3}
          </span>
        )}
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center space-x-4">
          {project.stars && (
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4" />
              <span>{formatNumber(project.stars)}</span>
            </div>
          )}
          {project.contributors && (
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span>{formatNumber(project.contributors)}</span>
            </div>
          )}
        </div>
        {project.language && (
          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {project.language}
          </span>
        )}
      </div>
    </div>
  )
}
EOF

cat > src/components/ui/SearchBar.tsx << 'EOF'
'use client'

import { Search } from 'lucide-react'

interface SearchBarProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export function SearchBar({ value, onChange, placeholder = "搜索项目..." }: SearchBarProps) {
  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        placeholder={placeholder}
      />
    </div>
  )
}
EOF

cat > src/components/ui/CategoryFilter.tsx << 'EOF'
'use client'

import { Category } from '@/types/landscape'

interface CategoryFilterProps {
  categories: Category[]
  selectedCategory: string
  onCategoryChange: (categoryId: string) => void
}

export function CategoryFilter({ categories, selectedCategory, onCategoryChange }: CategoryFilterProps) {
  return (
    <div className="flex flex-wrap gap-2">
      <button
        onClick={() => onCategoryChange('')}
        className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
          selectedCategory === ''
            ? 'bg-blue-600 text-white'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
      >
        全部
      </button>
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => onCategoryChange(category.id)}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            selectedCategory === category.id
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          style={{
            backgroundColor: selectedCategory === category.id ? category.color : undefined,
          }}
        >
          {category.name}
        </button>
      ))}
    </div>
  )
}
EOF

cat > src/components/ui/MaturityFilter.tsx << 'EOF'
'use client'

import { getMaturityColor, getMaturityLabel } from '@/lib/utils'

interface MaturityFilterProps {
  selectedMaturity: string[]
  onMaturityChange: (maturity: string[]) => void
}

const maturityLevels = ['graduated', 'incubating', 'sandbox', 'archived']

export function MaturityFilter({ selectedMaturity, onMaturityChange }: MaturityFilterProps) {
  const toggleMaturity = (maturity: string) => {
    if (selectedMaturity.includes(maturity)) {
      onMaturityChange(selectedMaturity.filter(m => m !== maturity))
    } else {
      onMaturityChange([...selectedMaturity, maturity])
    }
  }

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">成熟度筛选</h3>
      <div className="space-y-2">
        {maturityLevels.map((maturity) => (
          <label key={maturity} className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={selectedMaturity.includes(maturity)}
              onChange={() => toggleMaturity(maturity)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
            />
            <span className={`px-2 py-1 rounded text-xs font-medium ${getMaturityColor(maturity)}`}>
              {getMaturityLabel(maturity)}
            </span>
          </label>
        ))}
      </div>
    </div>
  )
}
EOF

echo "创建数据文件..."
# 创建示例数据（简化版本以适应脚本长度限制）
cat > public/data/landscape.json << 'EOF'
{
  "categories": [
    {
      "id": "container-runtime",
      "name": "容器运行时",
      "description": "容器化技术和运行时环境",
      "color": "#4F46E5"
    },
    {
      "id": "orchestration",
      "name": "编排调度",
      "description": "容器编排和调度平台",
      "color": "#059669"
    },
    {
      "id": "observability",
      "name": "可观测性",
      "description": "监控、日志和追踪工具",
      "color": "#DC2626"
    },
    {
      "id": "security",
      "name": "安全",
      "description": "安全扫描和策略管理",
      "color": "#7C2D12"
    },
    {
      "id": "storage",
      "name": "存储",
      "description": "数据存储和管理",
      "color": "#1D4ED8"
    },
    {
      "id": "networking",
      "name": "网络",
      "description": "网络和服务网格",
      "color": "#9333EA"
    }
  ],
  "projects": [
    {
      "id": "docker",
      "name": "Docker",
      "description": "容器化平台，用于构建、分发和运行应用程序",
      "homepage_url": "https://docker.com",
      "github_url": "https://github.com/docker/docker",
      "category": "container-runtime",
      "tags": ["容器", "运行时", "开发工具"],
      "maturity": "graduated",
      "stars": 68000,
      "contributors": 2800,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "kubernetes",
      "name": "Kubernetes",
      "description": "开源容器编排平台，用于自动化部署、扩展和管理容器化应用",
      "homepage_url": "https://kubernetes.io",
      "github_url": "https://github.com/kubernetes/kubernetes",
      "category": "orchestration",
      "tags": ["编排", "调度", "集群管理"],
      "maturity": "graduated",
      "stars": 110000,
      "contributors": 6800,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "prometheus",
      "name": "Prometheus",
      "description": "开源监控和告警工具包，专为云原生环境设计",
      "homepage_url": "https://prometheus.io",
      "github_url": "https://github.com/prometheus/prometheus",
      "category": "observability",
      "tags": ["监控", "指标", "告警"],
      "maturity": "graduated",
      "stars": 55000,
      "contributors": 1900,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "grafana",
      "name": "Grafana",
      "description": "开源分析和监控平台，支持多种数据源",
      "homepage_url": "https://grafana.com",
      "github_url": "https://github.com/grafana/grafana",
      "category": "observability",
      "tags": ["可视化", "仪表板", "分析"],
      "maturity": "graduated",
      "stars": 62000,
      "contributors": 2100,
      "license": "AGPL-3.0",
      "language": "TypeScript"
    },
    {
      "id": "containerd",
      "name": "containerd",
      "description": "行业标准的容器运行时，专注于简单性、健壮性和可移植性",
      "homepage_url": "https://containerd.io",
      "github_url": "https://github.com/containerd/containerd",
      "category": "container-runtime",
      "tags": ["容器", "运行时", "CRI"],
      "maturity": "graduated",
      "stars": 17000,
      "contributors": 420,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "helm",
      "name": "Helm",
      "description": "Kubernetes的包管理器，简化应用程序的部署和管理",
      "homepage_url": "https://helm.sh",
      "github_url": "https://github.com/helm/helm",
      "category": "orchestration",
      "tags": ["包管理", "部署", "模板"],
      "maturity": "graduated",
      "stars": 27000,
      "contributors": 1200,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "istio",
      "name": "Istio",
      "description": "开源服务网格，提供连接、保护、控制和观察服务的统一方式",
      "homepage_url": "https://istio.io",
      "github_url": "https://github.com/istio/istio",
      "category": "networking",
      "tags": ["服务网格", "安全", "流量管理"],
      "maturity": "graduated",
      "stars": 35000,
      "contributors": 1800,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "etcd",
      "name": "etcd",
      "description": "分布式可靠的键值存储，用于分布式系统的关键数据",
      "homepage_url": "https://etcd.io",
      "github_url": "https://github.com/etcd-io/etcd",
      "category": "storage",
      "tags": ["键值存储", "分布式", "一致性"],
      "maturity": "graduated",
      "stars": 47000,
      "contributors": 680,
      "license": "Apache-2.0",
      "language": "Go"
    },
    {
      "id": "falco",
      "name": "Falco",
      "description": "云原生运行时安全项目，检测异常活动和潜在安全威胁",
      "homepage_url": "https://falco.org",
      "github_url": "https://github.com/falcosecurity/falco",
      "category": "security",
      "tags": ["安全", "运行时检测", "威胁检测"],
      "maturity": "graduated",
      "stars": 7200,
      "contributors": 380,
      "license": "Apache-2.0",
      "language": "C++"
    },
    {
      "id": "jaeger",
      "name": "Jaeger",
      "description": "开源端到端分布式追踪系统",
      "homepage_url": "https://jaegertracing.io",
      "github_url": "https://github.com/jaegertracing/jaeger",
      "category": "observability",
      "tags": ["分布式追踪", "性能监控", "微服务"],
      "maturity": "graduated",
      "stars": 20000,
      "contributors": 420,
      "license": "Apache-2.0",
      "language": "Go"
    }
  ]
}
EOF

echo "创建主页面..."
# 创建简化版主页面
cat > src/app/page.tsx << 'EOF'
'use client'

import { useState, useEffect, useMemo } from 'react'
import { Project, Category, LandscapeData, FilterState } from '@/types/landscape'
import { ProjectCard } from '@/components/ui/ProjectCard'
import { SearchBar } from '@/components/ui/SearchBar'
import { CategoryFilter } from '@/components/ui/CategoryFilter'
import { MaturityFilter } from '@/components/ui/MaturityFilter'
import { Filter, X } from 'lucide-react'

export default function Home() {
  const [data, setData] = useState<LandscapeData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    maturity: [],
    tags: []
  })

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/data/landscape.json')
        const landscapeData: LandscapeData = await response.json()
        setData(landscapeData)
      } catch (error) {
        console.error('Failed to load landscape data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const filteredProjects = useMemo(() => {
    if (!data) return []

    return data.projects.filter(project => {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const matchesSearch = 
          project.name.toLowerCase().includes(searchTerm) ||
          project.description.toLowerCase().includes(searchTerm) ||
          project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        
        if (!matchesSearch) return false
      }

      if (filters.category && project.category !== filters.category) {
        return false
      }

      if (filters.maturity.length > 0 && !filters.maturity.includes(project.maturity)) {
        return false
      }

      return true
    })
  }, [data, filters])

  const stats = useMemo(() => {
    if (!data) return { total: 0, categories: 0, graduated: 0 }

    const graduated = data.projects.filter(p => p.maturity === 'graduated').length
    
    return {
      total: data.projects.length,
      categories: data.categories.length,
      graduated
    }
  }, [data])

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      maturity: [],
      tags: []
    })
  }

  const hasActiveFilters = filters.search || filters.category || filters.maturity.length > 0

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">加载数据失败</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              云原生技术全景图
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              探索云原生生态系统中的开源项目和工具
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-blue-800">总项目数</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-600">{stats.categories}</div>
                <div className="text-sm text-green-800">分类数</div>
              </div>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-600">{stats.graduated}</div>
                <div className="text-sm text-purple-800">已毕业项目</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 space-y-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1 max-w-md">
              <SearchBar
                value={filters.search}
                onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
                placeholder="搜索项目、描述或标签..."
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Filter className="w-4 h-4" />
                高级筛选
              </button>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                >
                  <X className="w-4 h-4" />
                  清除筛选
                </button>
              )}
            </div>
          </div>

          <div className="flex justify-center">
            <CategoryFilter
              categories={data.categories}
              selectedCategory={filters.category}
              onCategoryChange={(categoryId) => setFilters(prev => ({ ...prev, category: categoryId }))}
            />
          </div>

          {showFilters && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <MaturityFilter
                selectedMaturity={filters.maturity}
                onMaturityChange={(maturity) => setFilters(prev => ({ ...prev, maturity }))}
              />
            </div>
          )}
        </div>

        <div className="mb-6">
          <p className="text-gray-600 text-center">
            显示 {filteredProjects.length} 个项目
            {filters.search && ` (搜索: "${filters.search}")`}
            {filters.category && ` (分类: ${data.categories.find(c => c.id === filters.category)?.name})`}
          </p>
        </div>

        {filteredProjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">没有找到匹配的项目</p>
            <p className="text-gray-400 text-sm mt-2">尝试调整搜索条件或过滤器</p>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                清除所有筛选条件
              </button>
            )}
          </div>
        )}
      </main>

      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>&copy; 2024 云原生技术全景图. 基于开源项目构建.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
EOF

echo "更新样式..."
cat > src/app/globals.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
EOF

echo "🎉 完整的增强版landscape项目创建成功！"
echo "项目特性："
echo "✅ Next.js 15 + React 18 + TypeScript"
echo "✅ Tailwind CSS 响应式设计"
echo "✅ 10个云原生项目示例"
echo "✅ 6个主要分类"
echo "✅ 搜索功能"
echo "✅ 分类过滤"
echo "✅ 成熟度筛选"
echo "✅ 高级筛选面板"
echo "✅ 统计信息展示"
echo "✅ 项目卡片展示"
echo ""
echo "启动开发服务器..."
npm run dev